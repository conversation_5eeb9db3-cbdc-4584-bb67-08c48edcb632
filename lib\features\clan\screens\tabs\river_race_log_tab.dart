import 'package:cr/constant/color_constants.dart';
import 'package:cr/constant/custom_circular_loading.dart';
import 'package:cr/constant/custom_container.dart';
import 'package:cr/constant/custom_refresh_indicator.dart';
import 'package:cr/constant/navigation.dart';
import 'package:cr/constant/text_styles.dart';
import 'package:cr/features/clan/clan_provider/clan_provider.dart';
import 'package:cr/features/clan/screens/river_race_participants.dart';
import 'package:cr/theme/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

class RiverRaceLogTab extends ConsumerWidget {
  final String clanTag;
  const RiverRaceLogTab({super.key, required this.clanTag});

  @override
  Widget build(BuildContext context, ref) {
    final clanRiverRaceLog = ref.watch(
      clanRiverRaceLogProvider(clanTag.replaceAll('#', '')),
    );
    final isDarkMode = ref.watch(themeProvider).isDarkMode;

    return Container(
      decoration: BoxDecoration(color: Theme.of(context).colorScheme.surface),
      child: clanRiverRaceLog.when(
        data: (data) {
          return CustomRefreshIndicator(
            onRefresh: () async {
              ref.invalidate(clanRiverRaceLogProvider);
              return Future.delayed(const Duration(seconds: 2));
            },
            child: SingleChildScrollView(
              key: const PageStorageKey<String>('river_race_log_tab'),
              child: SafeArea(
                top: false,
                child: Column(
                  children: [
                    ListView.separated(
                      separatorBuilder:
                          (context, index) => SizedBox(height: 16.h),
                      key: const PageStorageKey<String>('river_race_log_tab'),
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      padding: EdgeInsets.symmetric(
                        horizontal: 12.w,
                        vertical: 10.h,
                      ),
                      itemCount: data.length,
                      itemBuilder: (context, index) {
                        final riverLog = data[index];
                        return Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 12.w,
                            vertical: 14.h,
                          ),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: ContainerBoxShadows.getBoxShadow(ref),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            spacing: 4.h,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Season ${riverLog.seasonId}',
                                    style: TextStyles.bodyTextWhiteStyles
                                        .copyWith(
                                          fontSize: 12.sp,
                                          color:
                                              isDarkMode
                                                  ? darkTextColor
                                                  : textColor,
                                        ),
                                  ),
                                  Text(
                                    DateFormat('dd MMM, yyyy').add_jm().format(
                                      DateTime.parse(riverLog.createdDate),
                                    ),
                                    style: TextStyles.bodyTextWhiteStyles
                                        .copyWith(
                                          fontSize: 9.sp,
                                          color:
                                              isDarkMode
                                                  ? darkTextColor
                                                  : textColor.withValues(
                                                    alpha: 0.7,
                                                  ),
                                        ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 8.h),
                              ListView.separated(
                                separatorBuilder:
                                    (context, index) => SizedBox(height: 14.h),
                                key: const PageStorageKey<String>(
                                  'river_race_log_tab',
                                ),
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                padding: EdgeInsets.zero,
                                itemCount: riverLog.standings.length,
                                itemBuilder: (context, standingIndex) {
                                  final standing =
                                      riverLog.standings[standingIndex];
                                  return GestureDetector(
                                    onTap: () {
                                      navigatePush(
                                        context,
                                        RiverRaceParticipants(
                                          participants:
                                              standing.clan.participants,
                                        ),
                                      );
                                    },
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 12.w,
                                        vertical: 10.h,
                                      ),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(8),
                                        color:
                                            Theme.of(
                                              context,
                                            ).colorScheme.surface,
                                        boxShadow:
                                            ContainerBoxShadows.getBoxShadow(
                                              ref,
                                            ),
                                      ),
                                      child: Column(
                                        spacing: 4.h,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                'Rank ${standing.rank}',
                                                style: TextStyles
                                                    .bodyTextWhiteStyles
                                                    .copyWith(
                                                      fontSize: 10.sp,
                                                      color:
                                                          isDarkMode
                                                              ? darkTextColor
                                                              : textColor,
                                                    ),
                                              ),
                                              Row(
                                                children: [
                                                  Text(
                                                    standing.trophyChange
                                                        .toString(),
                                                    style: TextStyles
                                                        .bodyTextWhiteStyles
                                                        .copyWith(
                                                          fontSize: 10.sp,
                                                          color:
                                                              isDarkMode
                                                                  ? darkTextColor
                                                                  : textColor,
                                                        ),
                                                  ),
                                                  SizedBox(width: 4.w),
                                                  Image.asset(
                                                    'assets/images/trophy.png',
                                                    height: 14.h,
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                          Text(
                                            standing.clan.name,
                                            style: TextStyles
                                                .bodyTextWhiteStyles
                                                .copyWith(
                                                  fontSize: 12.sp,
                                                  color:
                                                      isDarkMode
                                                          ? darkTextColor
                                                          : textColor,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                    SizedBox(height: 10.h),
                    Center(
                      child: Text(
                        'Clash Royale Stats',
                        style: TextStyles.bodyTextWhiteStyles.copyWith(
                          fontSize: 10.sp,
                          color: const Color(0xFF6D7F97),
                        ),
                      ),
                    ),
                    SizedBox(height: 10.h),
                  ],
                ),
              ),
            ),
          );
        },
        error: (error, stackTrace) => Center(child: Text(error.toString())),
        loading: () => Center(child: CustomCircularLoading()),
      ),
    );
  }
}
