import 'package:cr/constant/color_constants.dart';
import 'package:cr/constant/sized_box.dart';
import 'package:cr/constant/text_styles.dart';
import 'package:cr/theme/theme_provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AboutScreen extends ConsumerWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context, ref) {
    final isDarkMode = ref.watch(themeProvider).isDarkMode;
    return Scaffold(
      appBar: AppBar(
        title: const Text('About'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        leading: InkWell(
          onTap: () => Navigator.pop(context),
          child: Icon(
            CupertinoIcons.back,
            color: isDarkMode ? darkTextColor : textColor,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
        child: Safe<PERSON><PERSON>(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'About',
                style: TextStyles.titleTextStyles.copyWith(
                  color: isDarkMode ? darkTextColor : textColor,
                ),
              ),
              AppSize.v10,
              Text(
                'Hey there, fellow Clash Royale enthusiast!',
                style: TextStyles.bodyTextStyles.copyWith(
                  fontSize: 12.sp,
                  color: isDarkMode ? darkTextColor : textColor,
                ),
              ),
              AppSize.v10,
              Text(
                'What You\'ll Find Here',
                style: TextStyles.titleTextStyles.copyWith(
                  color: isDarkMode ? darkTextColor : textColor,
                ),
              ),
              AppSize.v10,
              FeatureItem(
                isDarkMode: isDarkMode,
                title: 'Your Player Stats',
                description:
                    'Everything from your trophy count and win rate to your favorite cards and recent battles',
              ),
              AppSize.v2,
              FeatureItem(
                isDarkMode: isDarkMode,
                title: 'Clan Insights',
                description:
                    'See how active your clanmates are, who\'s crushing it with donations, and how your clan stacks up overall',
              ),
              AppSize.v2,
              FeatureItem(
                isDarkMode: isDarkMode,
                title: 'Progress Over Time',
                description:
                    'Watch yourself improve with easy-to-read charts that show your journey',
              ),
              AppSize.v10,
              Text(
                'Just So You Know',
                style: TextStyles.titleTextStyles.copyWith(
                  color: isDarkMode ? darkTextColor : textColor,
                ),
              ),
              AppSize.v10,
              Text(
                'This is totally unofficial - just a fan project made by someone who loves the game. I\'m not connected to Supercell in any way, and all the Clash Royale stuff obviously belongs to them. I just wanted to create something cool for our community!',
                style: TextStyles.bodyTextStyles.copyWith(
                  fontSize: 12.sp,
                  color: isDarkMode ? darkTextColor : textColor,
                ),
              ),
              AppSize.v16,
              Text(
                'Your Data is Safe',
                style: TextStyles.titleTextStyles.copyWith(
                  color: isDarkMode ? darkTextColor : textColor,
                ),
              ),
              AppSize.v10,
              Text(
                'I only use the public stats that are already available through the game\'s API. No personal info gets stored, and I definitely don\'t need your account password or anything sketchy like that.',
                style: TextStyles.bodyTextStyles.copyWith(
                  fontSize: 12.sp,
                  color: isDarkMode ? darkTextColor : textColor,
                ),
              ),
              AppSize.v16,
              Center(
                child: Column(
                  children: [
                    Text(
                      'Hope this helps you dominate the arena! 🏆',
                      style: TextStyles.bodyTextStyles.copyWith(
                        fontSize: 12.sp,
                        color: isDarkMode ? darkTextColor : textColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    AppSize.v10,
                    Text(
                      'Made by a CR addict who probably plays way too much',
                      style: TextStyles.bodyTextStyles.copyWith(
                        fontSize: 10.sp,
                        color: isDarkMode ? darkTextColor : textColor,
                        fontStyle: FontStyle.italic,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class FeatureItem extends StatelessWidget {
  final String title;
  final String description;
  final bool isDarkMode;

  const FeatureItem({
    super.key,
    required this.title,
    required this.description,
    required this.isDarkMode,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '• ',
            style: TextStyles.bodyTextStyles.copyWith(
              fontSize: 18.sp,
              color: sPrimaryColor,
            ),
          ),
          Expanded(
            child: RichText(
              text: TextSpan(
                style: DefaultTextStyle.of(context).style,
                children: [
                  TextSpan(
                    text: '$title : ',
                    style: TextStyles.titleTextStyles.copyWith(
                      color: isDarkMode ? darkTextColor : textColor,
                      fontSize: 14.sp,
                    ),
                  ),
                  TextSpan(
                    text: description,
                    style: TextStyles.bodyTextStyles.copyWith(
                      color: isDarkMode ? darkTextColor : textColor,
                      fontSize: 12.sp,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
