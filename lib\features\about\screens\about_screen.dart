import 'package:cr/constant/color_constants.dart';
import 'package:cr/constant/text_styles.dart';
import 'package:cr/theme/theme_provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AboutScreen extends ConsumerWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDarkMode = ref.watch(themeProvider).isDarkMode;
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'About',
          style: TextStyles.titleTextStyles.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            color: isDarkMode ? darkTextColor : textColor,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: Container(
          margin: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            color: isDarkMode ? darkbgColor : bgColor,
            borderRadius: BorderRadius.circular(12.r),
            boxShadow: [
              BoxShadow(
                color:
                    isDarkMode
                        ? Colors.black.withValues(alpha: 0.3)
                        : Colors.white.withValues(alpha: 0.8),
                offset: const Offset(-2, -2),
                blurRadius: 4,
              ),
              BoxShadow(
                color:
                    isDarkMode
                        ? Colors.black.withValues(alpha: 0.3)
                        : const Color(0xFFA3B1C6).withValues(alpha: 0.6),
                offset: const Offset(2, 2),
                blurRadius: 4,
              ),
            ],
          ),
          child: InkWell(
            onTap: () => Navigator.pop(context),
            borderRadius: BorderRadius.circular(12.r),
            child: Icon(
              CupertinoIcons.back,
              color: isDarkMode ? darkTextColor : textColor,
              size: 20.sp,
            ),
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Hero Section
            _buildHeroSection(isDarkMode),
            SizedBox(height: 32.h),

            // Features Section
            _buildFeaturesSection(isDarkMode),
            SizedBox(height: 32.h),

            // Disclaimer Section
            _buildDisclaimerSection(isDarkMode),
            SizedBox(height: 32.h),

            // Privacy Section
            _buildPrivacySection(isDarkMode),
            SizedBox(height: 32.h),

            // Footer Section
            _buildFooterSection(isDarkMode),
            SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }

  Widget _buildHeroSection(bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            sPrimaryColor.withValues(alpha: 0.1),
            sPrimaryColor.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color:
                isDarkMode
                    ? Colors.black.withValues(alpha: 0.3)
                    : Colors.white.withValues(alpha: 0.8),
            offset: const Offset(-4, -4),
            blurRadius: 8,
          ),
          BoxShadow(
            color:
                isDarkMode
                    ? Colors.black.withValues(alpha: 0.3)
                    : const Color(0xFFA3B1C6).withValues(alpha: 0.6),
            offset: const Offset(4, 4),
            blurRadius: 8,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: sPrimaryColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Icon(
                  Icons.info_outline,
                  color: sPrimaryColor,
                  size: 24.sp,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Text(
                  'About This App',
                  style: TextStyles.titleTextStyles.copyWith(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? darkTextColor : textColor,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Text(
            'Hey there, fellow Clash Royale enthusiast! 👋',
            style: TextStyles.bodyTextStyles.copyWith(
              fontSize: 14.sp,
              color: isDarkMode ? darkTextColor : textColor,
              height: 1.5,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Welcome to your ultimate CR companion app, designed to help you dominate the arena with detailed stats and insights!',
            style: TextStyles.bodyTextStyles.copyWith(
              fontSize: 13.sp,
              color: (isDarkMode ? darkTextColor : textColor).withValues(
                alpha: 0.8,
              ),
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesSection(bool isDarkMode) {
    return _buildSection(
      isDarkMode: isDarkMode,
      title: 'What You\'ll Find Here',
      icon: Icons.star_outline,
      child: Column(
        children: [
          ModernFeatureItem(
            isDarkMode: isDarkMode,
            icon: Icons.person_outline,
            title: 'Your Player Stats',
            description:
                'Everything from your trophy count and win rate to your favorite cards and recent battles',
            color: const Color(0xFF4CAF50),
          ),
          SizedBox(height: 12.h),
          ModernFeatureItem(
            isDarkMode: isDarkMode,
            icon: Icons.group_outlined,
            title: 'Clan Insights',
            description:
                'See how active your clanmates are, who\'s crushing it with donations, and how your clan stacks up overall',
            color: const Color(0xFF2196F3),
          ),
          SizedBox(height: 12.h),
          ModernFeatureItem(
            isDarkMode: isDarkMode,
            icon: Icons.trending_up_outlined,
            title: 'Progress Over Time',
            description:
                'Watch yourself improve with easy-to-read charts that show your journey',
            color: const Color(0xFFFF9800),
          ),
        ],
      ),
    );
  }

  Widget _buildDisclaimerSection(bool isDarkMode) {
    return _buildSection(
      isDarkMode: isDarkMode,
      title: 'Just So You Know',
      icon: Icons.warning_amber_outlined,
      child: Text(
        'This is totally unofficial - just a fan project made by someone who loves the game. I\'m not connected to Supercell in any way, and all the Clash Royale stuff obviously belongs to them. I just wanted to create something cool for our community! 🎮',
        style: TextStyles.bodyTextStyles.copyWith(
          fontSize: 13.sp,
          color: (isDarkMode ? darkTextColor : textColor).withValues(
            alpha: 0.9,
          ),
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildPrivacySection(bool isDarkMode) {
    return _buildSection(
      isDarkMode: isDarkMode,
      title: 'Your Data is Safe',
      icon: Icons.security_outlined,
      child: Text(
        'I only use the public stats that are already available through the game\'s API. No personal info gets stored, and I definitely don\'t need your account password or anything sketchy like that. 🔒',
        style: TextStyles.bodyTextStyles.copyWith(
          fontSize: 13.sp,
          color: (isDarkMode ? darkTextColor : textColor).withValues(
            alpha: 0.9,
          ),
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildFooterSection(bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            sPrimaryColor.withValues(alpha: 0.05),
            sPrimaryColor.withValues(alpha: 0.1),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color:
                isDarkMode
                    ? Colors.black.withValues(alpha: 0.3)
                    : Colors.white.withValues(alpha: 0.8),
            offset: const Offset(-4, -4),
            blurRadius: 8,
          ),
          BoxShadow(
            color:
                isDarkMode
                    ? Colors.black.withValues(alpha: 0.3)
                    : const Color(0xFFA3B1C6).withValues(alpha: 0.6),
            offset: const Offset(4, 4),
            blurRadius: 8,
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: sPrimaryColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.emoji_events_outlined,
              color: sPrimaryColor,
              size: 32.sp,
            ),
          ),
          SizedBox(height: 16.h),
          Text(
            'Hope this helps you dominate the arena! 🏆',
            style: TextStyles.titleTextStyles.copyWith(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? darkTextColor : textColor,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8.h),
          Text(
            'Made by a CR addict who probably plays way too much',
            style: TextStyles.bodyTextStyles.copyWith(
              fontSize: 12.sp,
              color: (isDarkMode ? darkTextColor : textColor).withValues(
                alpha: 0.7,
              ),
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required bool isDarkMode,
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: isDarkMode ? darkbgColor : bgColor,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color:
                isDarkMode
                    ? Colors.black.withValues(alpha: 0.3)
                    : Colors.white.withValues(alpha: 0.8),
            offset: const Offset(-4, -4),
            blurRadius: 8,
          ),
          BoxShadow(
            color:
                isDarkMode
                    ? Colors.black.withValues(alpha: 0.3)
                    : const Color(0xFFA3B1C6).withValues(alpha: 0.6),
            offset: const Offset(4, 4),
            blurRadius: 8,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: sPrimaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Icon(icon, color: sPrimaryColor, size: 20.sp),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Text(
                  title,
                  style: TextStyles.titleTextStyles.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode ? darkTextColor : textColor,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          child,
        ],
      ),
    );
  }
}

class ModernFeatureItem extends StatelessWidget {
  final String title;
  final String description;
  final bool isDarkMode;
  final IconData icon;
  final Color color;

  const ModernFeatureItem({
    super.key,
    required this.title,
    required this.description,
    required this.isDarkMode,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(icon, color: color, size: 20.sp),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyles.titleTextStyles.copyWith(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode ? darkTextColor : textColor,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  description,
                  style: TextStyles.bodyTextStyles.copyWith(
                    fontSize: 12.sp,
                    color: (isDarkMode ? darkTextColor : textColor).withValues(
                      alpha: 0.8,
                    ),
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
