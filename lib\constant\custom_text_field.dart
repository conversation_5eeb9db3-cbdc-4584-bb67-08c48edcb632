import 'package:cr/constant/color_constants.dart';
import 'package:cr/constant/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomTextField extends StatelessWidget {
  final TextEditingController? controller;
  final void Function(String)? onFieldSubmitted;
  final bool isDarkMode;
  final String? hintText;
  const CustomTextField({
    super.key,
    required this.controller,
    this.onFieldSubmitted,
    this.isDarkMode = false,
    this.hintText,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      // cursorHeight: 18.h,
      style: TextStyles.bodyTextStyles.copyWith(
        fontSize: 10.sp,
        color: isDarkMode ? darkTextColor : textColor,
      ),
      onFieldSubmitted: onFieldSubmitted,
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: TextStyles.bodyTextStyles.copyWith(
          fontSize: 9.sp,
          color: isDarkMode ? darkTextColor : textColor,
        ),
        contentPadding: EdgeInsets.fromLTRB(0.w, 0.h, 0.w, 0.h),
        border: InputBorder.none,
        // border: OutlineInputBorder(
        //   borderRadius: BorderRadius.circular(10.r),
        //   borderSide: const BorderSide(color: Colors.grey),
        // ),
        // focusedBorder: OutlineInputBorder(
        //   borderRadius: BorderRadius.circular(10.r),
        //   borderSide: const BorderSide(color: sPrimaryColor),
        // ),
        // enabledBorder: OutlineInputBorder(
        //   borderRadius: BorderRadius.circular(10.r),
        //   borderSide: const BorderSide(color: Colors.grey),
        // ),
      ),
    );
  }
}
