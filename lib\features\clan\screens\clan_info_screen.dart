import 'package:cr/constant/color_constants.dart';
import 'package:cr/constant/text_styles.dart';
import 'package:cr/features/clan/clan_provider/clan_provider.dart';
import 'package:cr/features/clan/screens/tabs/clan_info_tab.dart';
import 'package:cr/features/clan/screens/tabs/river_race_log_tab.dart';
import 'package:cr/theme/theme_provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ClanInfoScreen extends ConsumerWidget {
  final String clanTag;
  const ClanInfoScreen({super.key, required this.clanTag});

  @override
  Widget build(BuildContext context, ref) {
    final clanInfo = ref.watch(clanInfoProvider(clanTag.replaceAll('#', '')));
    final isDarkMode = ref.watch(themeProvider).isDarkMode;
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 160.h,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage('assets/images/bg.jpg'),
                  fit: BoxFit.cover,
                ),
              ),
              child: Row(
                children: [
                  SizedBox(width: 8.w),
                  IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: Icon(CupertinoIcons.back, color: whiteColor),
                  ),
                  SizedBox(width: 10.w),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        clanInfo.whenOrNull(data: (data) => data.name) ?? '',
                        style: TextStyles.titleTextStyles.copyWith(
                          color: whiteColor,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        clanInfo.whenOrNull(data: (data) => data.tag) ?? '',
                        style: TextStyles.bodyTextStyles.copyWith(
                          fontSize: 9.sp,
                          color: whiteColor,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        'Members ${clanInfo.whenOrNull(data: (data) => data.members.toString())}',
                        style: TextStyles.bodyTextStyles.copyWith(
                          fontSize: 9.sp,
                          color: whiteColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Container(
              color: isDarkMode ? darkbgColor : bgColor,
              // decoration: BoxDecoration(
              //   image: DecorationImage(
              //     image: AssetImage('assets/images/cr_bg.jpg'),
              //     fit: BoxFit.fitWidth,
              //   ),
              // ),
              height: 40.h,
              child: TabBar(
                dividerHeight: 0.1,
                isScrollable: true,
                unselectedLabelColor: Colors.grey[600],
                indicatorSize: TabBarIndicatorSize.label,
                labelStyle: TextStyles.bodyTextStyles.copyWith(fontSize: 9.sp),
                tabAlignment: TabAlignment.start,
                padding: EdgeInsets.zero,
                indicatorColor: Colors.transparent,
                tabs: const [
                  Tab(text: 'Clan Info'),
                  Tab(text: 'River Race log'),
                ],
              ),
            ),
            Expanded(
              child: TabBarView(
                children: [
                  ClanInfoTab(clanTag: clanTag),
                  RiverRaceLogTab(clanTag: clanTag),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
