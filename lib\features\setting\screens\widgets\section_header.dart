import 'package:cr/constant/color_constants.dart';
import 'package:cr/constant/text_styles.dart';
import 'package:cr/theme/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SectionHeader extends ConsumerWidget {
  final String title;
  final IconData icon;
  final Color? iconColor;
  
  const SectionHeader({
    super.key,
    required this.title,
    required this.icon,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeState = ref.watch(themeProvider);
    
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        children: [
          Container(
            width: 32.w,
            height: 32.h,
            decoration: BoxDecoration(
              color: (iconColor ?? sPrimaryColor).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              icon,
              color: iconColor ?? sPrimaryColor,
              size: 18.sp,
            ),
          ),
          SizedBox(width: 12.w),
          Text(
            title,
            style: TextStyles.titleTextStyles.copyWith(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: themeState.isDarkMode ? darkTextColor : textColor,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Container(
              height: 1.h,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    (iconColor ?? sPrimaryColor).withValues(alpha: 0.3),
                    Colors.transparent,
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
