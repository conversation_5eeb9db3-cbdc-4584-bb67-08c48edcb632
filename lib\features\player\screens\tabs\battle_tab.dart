import 'package:cr/constant/color_constants.dart';
import 'package:cr/constant/custom_circular_loading.dart';
import 'package:cr/constant/custom_container.dart';
import 'package:cr/constant/text_styles.dart';
import 'package:cr/features/player/provider/player_provider.dart';
import 'package:cr/theme/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:intl/intl.dart';

class BattleTab extends ConsumerWidget {
  final String playerTag;
  const BattleTab({super.key, required this.playerTag});

  // calculate battle battleTime
  String battleTime(String battleTime) {
    DateTime dateTime = DateTime.parse(battleTime);
    DateTime now = DateTime.now();
    Duration difference = now.difference(dateTime);
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} min ago';
    } else if (difference.inHours < 24) {
      int hours = difference.inHours;
      int minutes = difference.inMinutes.remainder(60);
      return minutes > 0 ? '$hours hours ago' : '${hours}h ago';
    } else if (difference.inDays < 30) {
      return '${difference.inDays} days ago';
    } else {
      return DateFormat('yyyy-MM-dd').format(dateTime);
    }
  }

  @override
  Widget build(BuildContext context, ref) {
    final battleLog = ref.watch(playerBattleLogProvider(playerTag));
    final isDarkMode = ref.watch(themeProvider).isDarkMode;
    return Container(
      decoration: BoxDecoration(color: Theme.of(context).colorScheme.surface),
      child: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(playerBattleLogProvider(playerTag));
          return Future.delayed(const Duration(seconds: 2));
        },
        child:
            battleLog.isLoad
                ? Center(child: CustomCircularLoading())
                : battleLog.battleLog.isEmpty
                ? Center(
                  child: Text(
                    'No battles available',
                    style: TextStyles.bodyTextWhiteStyles.copyWith(
                      fontSize: 10.sp,
                      color: isDarkMode ? darkTextColor : textColor,
                    ),
                  ),
                )
                : battleLog.isError
                ? Center(
                  child: Text(
                    battleLog.errMessage,
                    style: TextStyles.bodyTextWhiteStyles.copyWith(
                      color: isDarkMode ? darkTextColor : textColor,
                    ),
                  ),
                )
                : SafeArea(
                  top: false,
                  child: ListView.separated(
                    key: const PageStorageKey<String>('battleLog'),
                    padding: EdgeInsets.symmetric(
                      horizontal: 12.w,
                      vertical: 14.h,
                    ),
                    separatorBuilder:
                        (context, index) => SizedBox(height: 16.h),
                    itemCount: battleLog.battleLog.length,
                    itemBuilder: (context, index) {
                      final battle = battleLog.battleLog[index];

                      //---- team crowns and opponent crowns
                      num teamCrowns = battle.team
                          .map((e) => e.crowns)
                          .reduce((a, b) => a + b);
                      num opponentCrowns = battle.opponent
                          .map((e) => e.crowns)
                          .reduce((a, b) => a + b);
                      String result;
                      if (teamCrowns > opponentCrowns) {
                        result = 'Victory';
                      } else if (teamCrowns < opponentCrowns) {
                        result = 'Defeat';
                      } else {
                        result = 'Draw';
                      }
                      //---------end--------------
                      return Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 10.w,
                          vertical: 8.h,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: Theme.of(context).colorScheme.surface,
                          boxShadow: ContainerBoxShadows.getBoxShadow(ref),
                        ),
                        child: Stack(
                          children: [
                            Column(
                              spacing: 4.h,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Center(
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        teamCrowns.toString(),
                                        style: TextStyles.bodyTextWhiteStyles
                                            .copyWith(
                                              fontSize: 12.sp,
                                              color:
                                                  isDarkMode
                                                      ? darkTextColor
                                                      : textColor,
                                            ),
                                      ),
                                      Text(
                                        ' - ',
                                        style: TextStyles.bodyTextWhiteStyles
                                            .copyWith(
                                              fontSize: 12.sp,
                                              color:
                                                  isDarkMode
                                                      ? darkTextColor
                                                      : textColor,
                                            ),
                                      ),
                                      Text(
                                        opponentCrowns.toString(),
                                        style: TextStyles.bodyTextWhiteStyles
                                            .copyWith(
                                              fontSize: 12.sp,
                                              color:
                                                  isDarkMode
                                                      ? darkTextColor
                                                      : textColor,
                                            ),
                                      ),
                                    ],
                                  ),
                                ),
                                Row(
                                  spacing: 8.w,
                                  children: [
                                    Expanded(
                                      child: ListView.builder(
                                        key: const PageStorageKey<String>(
                                          'battleLog',
                                        ),
                                        padding: EdgeInsets.zero,
                                        itemCount: battle.team.length,
                                        shrinkWrap: true,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        itemBuilder: (context, teamIndex) {
                                          final teamData =
                                              battle.team[teamIndex];
                                          return Column(
                                            spacing: 2.h,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              SizedBox(height: 4.h),
                                              Text(
                                                teamData.name,
                                                style: TextStyles
                                                    .bodyTextWhiteStyles
                                                    .copyWith(
                                                      fontSize: 12.sp,
                                                      color:
                                                          isDarkMode
                                                              ? darkTextColor
                                                              : textColor,
                                                    ),
                                              ),
                                              Text(
                                                teamData.clan.name.isEmpty
                                                    ? 'No Clan'
                                                    : teamData.clan.name,
                                                style: TextStyles
                                                    .bodyTextWhiteStyles
                                                    .copyWith(
                                                      fontSize: 9.sp,
                                                      color:
                                                          isDarkMode
                                                              ? darkTextColor
                                                              : textColor,
                                                    ),
                                              ),
                                              MasonryGridView.builder(
                                                gridDelegate:
                                                    const SliverSimpleGridDelegateWithFixedCrossAxisCount(
                                                      crossAxisCount: 4,
                                                    ),
                                                padding: EdgeInsets.zero,
                                                shrinkWrap: true,
                                                physics:
                                                    const NeverScrollableScrollPhysics(),
                                                itemCount:
                                                    teamData.cards.length,
                                                mainAxisSpacing: 2.h,
                                                crossAxisSpacing: 4.w,
                                                itemBuilder: (
                                                  context,
                                                  cardIndex,
                                                ) {
                                                  final card =
                                                      teamData.cards[cardIndex];
                                                  return Image.network(
                                                    card.iconUrl.medium,
                                                    height: 55.h,
                                                    width: 40.w,
                                                    fit: BoxFit.fitWidth,
                                                  );
                                                },
                                              ),
                                              Row(
                                                spacing: 4.w,
                                                children: [
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                          top: 2.0,
                                                        ),
                                                    child: Image.asset(
                                                      'assets/images/trophy.png',
                                                      height: 14.h,
                                                    ),
                                                  ),
                                                  Text(
                                                    teamData.startingTrophies
                                                        .toString(),
                                                    style: TextStyles
                                                        .bodyTextWhiteStyles
                                                        .copyWith(
                                                          fontSize: 9.sp,
                                                          color:
                                                              isDarkMode
                                                                  ? darkTextColor
                                                                  : textColor,
                                                        ),
                                                  ),
                                                ],
                                              ),
                                              SizedBox(height: 2.h),
                                              Text(
                                                teamData.trophyChange
                                                    .toString(),
                                                style: TextStyles
                                                    .bodyTextWhiteStyles
                                                    .copyWith(
                                                      fontSize: 9.sp,
                                                      color:
                                                          isDarkMode
                                                              ? darkTextColor
                                                              : textColor,
                                                    ),
                                              ),
                                            ],
                                          );
                                        },
                                      ),
                                    ),
                                    Expanded(
                                      child: ListView.builder(
                                        key: const PageStorageKey<String>(
                                          'battleLog',
                                        ),
                                        padding: EdgeInsets.zero,
                                        itemCount: battle.opponent.length,
                                        shrinkWrap: true,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        itemBuilder: (context, teamIndex) {
                                          final opponentData =
                                              battle.opponent[teamIndex];
                                          return Column(
                                            spacing: 2.h,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.end,
                                            children: [
                                              SizedBox(height: 4.h),
                                              Text(
                                                opponentData.name,
                                                style: TextStyles
                                                    .bodyTextWhiteStyles
                                                    .copyWith(
                                                      fontSize: 12.sp,
                                                      color:
                                                          isDarkMode
                                                              ? darkTextColor
                                                              : textColor,
                                                    ),
                                              ),
                                              Text(
                                                opponentData.clan.name.isEmpty
                                                    ? 'No Clan'
                                                    : opponentData.clan.name,
                                                style: TextStyles
                                                    .bodyTextWhiteStyles
                                                    .copyWith(
                                                      fontSize: 9.sp,
                                                      color:
                                                          isDarkMode
                                                              ? darkTextColor
                                                              : textColor,
                                                    ),
                                              ),
                                              MasonryGridView.builder(
                                                key: const PageStorageKey<
                                                  String
                                                >('battleLog'),
                                                gridDelegate:
                                                    const SliverSimpleGridDelegateWithFixedCrossAxisCount(
                                                      crossAxisCount: 4,
                                                    ),
                                                mainAxisSpacing: 0.h,
                                                crossAxisSpacing: 0.w,
                                                padding: EdgeInsets.zero,
                                                shrinkWrap: true,
                                                physics:
                                                    const NeverScrollableScrollPhysics(),
                                                itemCount:
                                                    opponentData.cards.length,
                                                itemBuilder: (
                                                  context,
                                                  cardIndex,
                                                ) {
                                                  final card =
                                                      opponentData
                                                          .cards[cardIndex];
                                                  return Image.network(
                                                    card.iconUrl.medium,
                                                    height: 55.h,
                                                    width: 40.w,
                                                    fit: BoxFit.fitWidth,
                                                  );
                                                },
                                              ),
                                              Row(
                                                spacing: 4.w,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.end,
                                                children: [
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                          top: 2.0,
                                                        ),
                                                    child: Image.asset(
                                                      'assets/images/trophy.png',
                                                      height: 14.h,
                                                    ),
                                                  ),
                                                  Text(
                                                    opponentData
                                                        .startingTrophies
                                                        .toString(),
                                                    style: TextStyles
                                                        .bodyTextWhiteStyles
                                                        .copyWith(
                                                          fontSize: 9.sp,
                                                          color:
                                                              isDarkMode
                                                                  ? darkTextColor
                                                                  : textColor,
                                                        ),
                                                  ),
                                                ],
                                              ),
                                              SizedBox(height: 2.h),
                                              Text(
                                                opponentData.trophyChange
                                                    .toString(),
                                                style: TextStyles
                                                    .bodyTextWhiteStyles
                                                    .copyWith(
                                                      fontSize: 9.sp,
                                                      color:
                                                          isDarkMode
                                                              ? darkTextColor
                                                              : textColor,
                                                    ),
                                              ),
                                            ],
                                          );
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 2.h),
                                Text(
                                  battleTime(battle.battleTime),
                                  style: TextStyles.bodyTextWhiteStyles
                                      .copyWith(
                                        fontSize: 8.sp,
                                        color:
                                            isDarkMode
                                                ? darkTextColor
                                                : textColor,
                                      ),
                                ),
                              ],
                            ),
                            Positioned(
                              top: 0,
                              left: 0,
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 8.w,
                                  vertical: 4.h,
                                ),
                                decoration: BoxDecoration(
                                  color:
                                      result == 'Victory'
                                          ? const Color(
                                            0xFF2DBA6A,
                                          ).withValues(alpha: 0.2)
                                          : Colors.redAccent.withValues(
                                            alpha: 0.2,
                                          ),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  result,
                                  style: TextStyles.bodyTextWhiteStyles
                                      .copyWith(
                                        fontSize: 10.sp,
                                        color:
                                            result == 'Victory'
                                                ? const Color(0xFF2DBA6A)
                                                : Colors.redAccent,
                                      ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
      ),
    );
  }
}
