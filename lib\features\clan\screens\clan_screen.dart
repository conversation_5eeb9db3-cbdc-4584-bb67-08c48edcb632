import 'dart:developer';

import 'package:cr/admob_service/admob_service.dart';
import 'package:cr/common_model/location_model.dart';
import 'package:cr/common_provider/location_provider.dart';
import 'package:cr/constant/color_constants.dart';
import 'package:cr/constant/custom_circular_loading.dart';
import 'package:cr/constant/custom_container.dart';
import 'package:cr/constant/custom_search_dropdown.dart';
import 'package:cr/constant/custom_text_field.dart';
import 'package:cr/constant/navigation.dart';
import 'package:cr/constant/sized_box.dart';
import 'package:cr/constant/text_styles.dart';
import 'package:cr/features/clan/clan_provider/clan_provider.dart';
import 'package:cr/features/clan/screens/clan_info_screen.dart';
import 'package:cr/theme/theme_provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

final selectedLocationProvider = StateProvider<LocationModel?>((ref) => null);

class ClanScreen extends ConsumerStatefulWidget {
  const ClanScreen({super.key});

  @override
  ConsumerState<ClanScreen> createState() => _ClanScreenState();
}

class _ClanScreenState extends ConsumerState<ClanScreen> {
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  BannerAd? banner;

  @override
  void initState() {
    super.initState();
    _createBannerAd();
  }

  void _createBannerAd() {
    banner = BannerAd(
      adUnitId: AdmobService.bannerAdUnitId!,
      request: const AdRequest(),
      size: AdSize.fullBanner,
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          setState(() {
            log("Banner Ad Loaded: ${ad.adUnitId}");
          });
        },
        onAdFailedToLoad: (ad, error) {
          ad.dispose();
          log("Banner Ad Failed to Load: ${error.message}");
        },
      ),
    )..load();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final locations = ref.watch(allLocationProvider);
    final selectedLocation = ref.watch(selectedLocationProvider);
    final themeState = ref.watch(themeProvider);

    return Scaffold(
      appBar: AppBar(
        title:
            !_isSearching
                ? const Text('Clan')
                : CustomTextField(
                  hintText: 'Search Clan (exclude #)',
                  controller: _searchController,
                  isDarkMode: themeState.isDarkMode,
                  onFieldSubmitted: (p0) {
                    navigatePush(
                      context,
                      ClanInfoScreen(clanTag: _searchController.text.trim()),
                    );
                  },
                ),
        elevation: 0,
        actions: [
          Container(
            margin: EdgeInsets.only(right: 16.w),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: themeState.isDarkMode ? darkbgColor : bgColor,
                shape: BoxShape.circle,
                boxShadow: ContainerBoxShadows.getBoxShadow(ref),
              ),
              child: IconButton(
                icon: Icon(
                  _isSearching ? CupertinoIcons.xmark : CupertinoIcons.search,
                  color: themeState.isDarkMode ? darkTextColor : textColor,
                  size: 22,
                ),
                onPressed: () {
                  setState(() {
                    _isSearching = !_isSearching;
                    if (!_isSearching) {
                      _searchController.clear();
                    }
                  });
                },
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: locations.when(
              data: (data) {
                if (data.isEmpty) {
                  return const Center(child: Text("No data available"));
                }
                if (selectedLocation == null) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    ref.read(selectedLocationProvider.notifier).state =
                        data.first;
                  });
                }
                return Column(
                  children: [
                    AppSize.v8,
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 14.w,
                        vertical: 8.h,
                      ),
                      child: Container(
                        height: 40.h,
                        decoration: BoxDecoration(
                          color: themeState.isDarkMode ? darkbgColor : bgColor,
                          borderRadius: BorderRadius.circular(8.r),
                          boxShadow: ContainerBoxShadows.getBoxShadow(ref),
                        ),
                        child: CustomSearchDropDown<LocationModel>(
                          isDarkMode: themeState.isDarkMode,
                          showSearchBox: true,
                          selectedItem: selectedLocation,
                          hintText: 'Choose Country',
                          items: (p0, p1) async => data,
                          compareFn: (p0, p1) => p0.name == p1.name,
                          itemAsString:
                              (LocationModel? item) => item?.name ?? '',
                          onChanged: (val) {
                            ref.read(selectedLocationProvider.notifier).state =
                                val;
                          },
                        ),
                      ),
                    ),
                    AppSize.v4,
                    if (selectedLocation != null)
                      _buildClanList(
                        ref,
                        selectedLocation.id.toString(),
                        themeState.isDarkMode,
                      ),
                  ],
                );
              },
              loading: () => Center(child: CustomCircularLoading()),
              error: (error, stackTrace) => Text(error.toString()),
            ),
          ),
          banner == null
              ? Container()
              : Container(
                height: 50,
                color: themeState.isDarkMode ? darkbgColor : bgColor,
                child: AdWidget(ad: banner!),
              ),
        ],
      ),
    );
  }

  Widget _buildClanList(WidgetRef ref, String locationId, bool isDarkMode) {
    final clanData = ref.watch(clanProvider(locationId));

    return clanData.when(
      data: (clans) {
        if (clans.isEmpty) {
          return const Center(child: Text("No clans available"));
        }

        return Expanded(
          child: ListView.separated(
            padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 12.h),
            separatorBuilder: (context, index) => SizedBox(height: 18.h),
            itemCount: clans.length,
            itemBuilder: (context, index) {
              final clan = clans[index];
              return Row(
                children: [
                  Container(
                    alignment: Alignment.center,
                    width: 30.w,
                    height: 30.h,
                    decoration: BoxDecoration(
                      color: isDarkMode ? darkbgColor : bgColor,
                      shape: BoxShape.circle,
                      boxShadow: ContainerBoxShadows.getBoxShadow(ref),
                    ),
                    child: Text(
                      clan.rank.toString(),
                      style: TextStyles.bodyTextWhiteStyles.copyWith(
                        fontSize: 10.sp,
                        color: isDarkMode ? darkTextColor : textColor,
                      ),
                    ),
                  ),
                  AppSize.h12,
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        navigatePush(
                          context,
                          ClanInfoScreen(clanTag: clan.tag),
                        );
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 10.w,
                          vertical: 8.h,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8.r),
                          color: isDarkMode ? darkbgColor : bgColor,
                          boxShadow: ContainerBoxShadows.getBoxShadow(ref),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Column(
                                spacing: 4.h,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    clan.name,
                                    style: TextStyles.bodyTextWhiteStyles
                                        .copyWith(
                                          fontSize: 12.sp,
                                          color:
                                              isDarkMode
                                                  ? darkTextColor
                                                  : textColor,
                                        ),
                                  ),
                                  Text(
                                    '${clan.members} Members',
                                    style: TextStyles.bodyTextWhiteStyles
                                        .copyWith(
                                          fontSize: 9.sp,
                                          color:
                                              isDarkMode
                                                  ? darkTextColor
                                                  : textColor,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                            Row(
                              children: [
                                Text(
                                  clan.clanScore.toString(),
                                  style: TextStyles.bodyTextWhiteStyles
                                      .copyWith(
                                        fontSize: 12.sp,
                                        color:
                                            isDarkMode
                                                ? darkTextColor
                                                : textColor,
                                      ),
                                ),
                                SizedBox(width: 4.w),
                                Image.asset(
                                  'assets/images/trophy.png',
                                  width: 20.w,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        );
      },
      loading: () => const Center(child: CustomCircularLoading()),
      error: (error, stackTrace) => Center(child: Text('Error: $error')),
    );
  }
}
