import 'package:cr/constant/color_constants.dart';
import 'package:cr/constant/custom_circular_loading.dart';
import 'package:cr/constant/custom_container.dart';
import 'package:cr/constant/navigation.dart';
import 'package:cr/constant/sized_box.dart';
import 'package:cr/constant/text_styles.dart';
import 'package:cr/features/clan/clan_provider/clan_provider.dart';
import 'package:cr/features/clan/screens/widgets/card_row.dart';
import 'package:cr/features/player/screens/player_info_screen.dart';
import 'package:cr/theme/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ClanInfoTab extends ConsumerWidget {
  final String clanTag;
  const ClanInfoTab({super.key, required this.clanTag});

  @override
  Widget build(BuildContext context, ref) {
    final clanInfo = ref.watch(clanInfoProvider(clanTag.replaceAll('#', '')));
    final isDarkMode = ref.watch(themeProvider).isDarkMode;

    return Container(
      decoration: BoxDecoration(color: isDarkMode ? darkbgColor : bgColor),
      child: clanInfo.when(
        data: (data) {
          return RefreshIndicator(
            onRefresh: () async {
              ref.invalidate(clanInfoProvider);
              return Future.delayed(const Duration(seconds: 2));
            },
            child: SingleChildScrollView(
              key: const PageStorageKey<String>('memberList'),
              child: SafeArea(
                top: false,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: EdgeInsets.all(12.w),
                      padding: EdgeInsets.symmetric(
                        horizontal: 12.w,
                        vertical: 18.h,
                      ),
                      decoration: BoxDecoration(
                        color: isDarkMode ? darkbgColor : bgColor,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: ContainerBoxShadows.getBoxShadow(ref),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        spacing: 4.h,
                        children: [
                          Text(
                            'Clan',
                            style: TextStyles.bodyTextWhiteStyles.copyWith(
                              fontSize: 12.sp,
                              color: isDarkMode ? darkTextColor : textColor,
                            ),
                          ),
                          SizedBox(height: 2.h),
                          CardRow(
                            title: 'Clan Name',
                            value: data.name,
                            isDarkMode: isDarkMode,
                          ),
                          CardRow(
                            title: 'Clan Trophies',
                            value: data.clanScore.toString(),
                            isDarkMode: isDarkMode,
                          ),
                          CardRow(
                            title: 'Required Trophies',
                            value: data.requiredTrophies.toString(),
                            isDarkMode: isDarkMode,
                          ),
                          CardRow(
                            title: 'Donation per week',
                            value: data.donationsPerWeek.toString(),
                            isDarkMode: isDarkMode,
                          ),
                          CardRow(
                            title: 'Clan War Trophies',
                            value: data.clanWarTrophies.toString(),
                            isDarkMode: isDarkMode,
                          ),
                          CardRow(
                            title: 'Type',
                            value: data.type,
                            isDarkMode: isDarkMode,
                          ),
                        ],
                      ),
                    ),
                    AppSize.v10,
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 12.w),
                      padding: EdgeInsets.symmetric(
                        horizontal: 12.w,
                        vertical: 18.h,
                      ),
                      decoration: BoxDecoration(
                        color: isDarkMode ? darkbgColor : bgColor,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: ContainerBoxShadows.getBoxShadow(ref),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        spacing: 4.h,
                        children: [
                          Text(
                            'Description',
                            style: TextStyles.bodyTextWhiteStyles.copyWith(
                              fontSize: 12.sp,
                              color: isDarkMode ? darkTextColor : textColor,
                            ),
                          ),
                          AppSize.v2,
                          Text(
                            data.description,
                            style: TextStyles.bodyTextWhiteStyles.copyWith(
                              fontSize: 9.sp,
                              color: isDarkMode ? darkTextColor : textColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    AppSize.v4,
                    Container(
                      margin: EdgeInsets.all(12.w),
                      padding: EdgeInsets.symmetric(
                        horizontal: 12.w,
                        vertical: 18.h,
                      ),
                      decoration: BoxDecoration(
                        color: isDarkMode ? darkbgColor : bgColor,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: ContainerBoxShadows.getBoxShadow(ref),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        spacing: 4.h,
                        children: [
                          Text(
                            'Members ${data.memberList.length}/50',
                            style: TextStyles.bodyTextWhiteStyles.copyWith(
                              fontSize: 12.sp,
                              color: isDarkMode ? darkTextColor : textColor,
                            ),
                          ),
                          AppSize.v8,
                          ListView.separated(
                            key: const PageStorageKey<String>('memberList'),
                            padding: EdgeInsets.zero,
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            separatorBuilder: (context, index) => AppSize.v16,
                            itemCount: data.memberList.length,
                            itemBuilder: (context, index) {
                              final memberInfo = data.memberList[index];
                              return Row(
                                children: [
                                  Container(
                                    alignment: Alignment.center,
                                    width: 30.w,
                                    height: 30.h,
                                    decoration: BoxDecoration(
                                      color: isDarkMode ? darkbgColor : bgColor,
                                      shape: BoxShape.circle,
                                      boxShadow:
                                          ContainerBoxShadows.getBoxShadow(ref),
                                    ),
                                    child: Text(
                                      memberInfo.clanRank.toString(),
                                      style: TextStyles.bodyTextWhiteStyles
                                          .copyWith(
                                            fontSize: 10.sp,
                                            color:
                                                isDarkMode
                                                    ? darkTextColor
                                                    : textColor,
                                          ),
                                    ),
                                  ),
                                  AppSize.h16,
                                  Expanded(
                                    child: GestureDetector(
                                      onTap: () {
                                        navigatePush(
                                          context,
                                          PlayerInfoScreen(
                                            playerTag: memberInfo.tag
                                                .replaceAll('#', ''),
                                          ),
                                        );
                                      },
                                      child: Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 10.w,
                                          vertical: 8.h,
                                        ),
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                          color:
                                              isDarkMode
                                                  ? darkbgColor
                                                  : bgColor,
                                          boxShadow:
                                              ContainerBoxShadows.getBoxShadow(
                                                ref,
                                              ),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Flexible(
                                              child: Column(
                                                spacing: 4.h,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    memberInfo.name,
                                                    style: TextStyles
                                                        .bodyTextWhiteStyles
                                                        .copyWith(
                                                          fontSize: 12.sp,
                                                          color:
                                                              isDarkMode
                                                                  ? darkTextColor
                                                                  : textColor,
                                                        ),
                                                  ),
                                                  Text(
                                                    memberInfo.role,
                                                    style: TextStyles
                                                        .bodyTextWhiteStyles
                                                        .copyWith(
                                                          fontSize: 9.sp,
                                                          color:
                                                              isDarkMode
                                                                  ? darkTextColor
                                                                  : textColor,
                                                        ),
                                                  ),
                                                  Text(
                                                    'Last Online: ${_getTimeAgo(memberInfo.lastSeen)}',
                                                    style: TextStyles
                                                        .bodyTextWhiteStyles
                                                        .copyWith(
                                                          fontSize: 7.sp,
                                                          color:
                                                              isDarkMode
                                                                  ? darkTextColor
                                                                  : textColor
                                                                      .withValues(
                                                                        alpha:
                                                                            0.7,
                                                                      ),
                                                        ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Row(
                                              children: [
                                                Text(
                                                  memberInfo.trophies
                                                      .toString(),
                                                  style: TextStyles
                                                      .bodyTextWhiteStyles
                                                      .copyWith(
                                                        fontSize: 12.sp,
                                                        color:
                                                            isDarkMode
                                                                ? darkTextColor
                                                                : textColor,
                                                      ),
                                                ),
                                                AppSize.h4,
                                                Image.asset(
                                                  'assets/images/trophy.png',
                                                  width: 20.w,
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                    AppSize.v10,
                    Center(
                      child: Text(
                        'Clash Royale Stats',
                        style: TextStyles.bodyTextWhiteStyles.copyWith(
                          fontSize: 10.sp,
                          color: isDarkMode ? darkTextColor : textColor,
                        ),
                      ),
                    ),
                    AppSize.v10,
                  ],
                ),
              ),
            ),
          );
        },
        error: (error, stackTrace) => Center(child: Text(error.toString())),
        loading: () => Center(child: CustomCircularLoading()),
      ),
    );
  }
}

String _getTimeAgo(String lastSeen) {
  final now = DateTime.now();
  final lastSeenDateTime = DateTime.parse(lastSeen);
  final difference = now.difference(lastSeenDateTime);

  if (difference.inDays >= 365) {
    final years = difference.inDays ~/ 365;
    return '$years year${years > 1 ? 's' : ''} ago';
  } else if (difference.inDays >= 30) {
    final months = difference.inDays ~/ 30;
    return '$months month${months > 1 ? 's' : ''} ago';
  } else if (difference.inDays >= 7) {
    final weeks = difference.inDays ~/ 7;
    return '$weeks week${weeks > 1 ? 's' : ''} ago';
  } else if (difference.inDays >= 1) {
    return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
  } else if (difference.inHours >= 1) {
    return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
  } else if (difference.inMinutes >= 1) {
    return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
  } else {
    return 'just now';
  }
}
