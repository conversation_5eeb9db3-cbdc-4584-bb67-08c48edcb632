import 'package:cr/constant/color_constants.dart';
import 'package:cr/constant/common_snackbar.dart';
import 'package:cr/constant/custom_circular_loading.dart';
import 'package:cr/constant/text_styles.dart';
import 'package:cr/features/hive/model/player_model.dart';
import 'package:cr/features/hive/provider/player_provider_hive.dart';
import 'package:cr/features/player/provider/player_provider.dart';
import 'package:cr/features/player/screens/tabs/battle_tab.dart';
import 'package:cr/features/player/screens/tabs/profile_tab.dart';
import 'package:cr/features/upcoming_chest/screens/upcoming_chest_tab.dart';
import 'package:cr/theme/theme_provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PlayerInfoScreen extends ConsumerStatefulWidget {
  final String playerTag;
  const PlayerInfoScreen({super.key, required this.playerTag});

  @override
  ConsumerState<PlayerInfoScreen> createState() => _PlayerInfoScreenState();
}

class _PlayerInfoScreenState extends ConsumerState<PlayerInfoScreen> {
  @override
  Widget build(BuildContext context) {
    final favData = ref.watch(playerModelHiveProvider);
    final favTag = favData.map((e) => e.playerTag).toList();
    final playerData = ref.watch(playerInfoProvider(widget.playerTag));
    final isDarkMode = ref.watch(themeProvider).isDarkMode;
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        body:
            playerData.isError
                ? Center(child: Text(playerData.errMessage))
                : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Stack(
                      children: [
                        Container(
                          height: 160.h,
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage('assets/images/bg.jpg'),
                              fit: BoxFit.cover,
                            ),
                          ),
                          child: Row(
                            children: [
                              SizedBox(width: 8.w),
                              IconButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                },
                                icon: Icon(
                                  CupertinoIcons.back,
                                  color: whiteColor,
                                ),
                              ),
                              SizedBox(width: 10.w),
                              Flexible(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      playerData.playerInfo.name,
                                      style: TextStyles.titleTextStyles
                                          .copyWith(color: whiteColor),
                                    ),
                                    SizedBox(height: 4.h),
                                    Text(
                                      playerData
                                              .playerInfo
                                              .playerClan
                                              .name
                                              .isEmpty
                                          ? 'No Clan'
                                          : playerData
                                              .playerInfo
                                              .playerClan
                                              .name,
                                      style: TextStyles.bodyTextStyles.copyWith(
                                        fontSize: 9.sp,
                                        color: whiteColor,
                                      ),
                                    ),
                                    SizedBox(height: 4.h),
                                    Text(
                                      playerData.playerInfo.tag,
                                      style: TextStyles.bodyTextStyles.copyWith(
                                        fontSize: 9.sp,
                                        color: whiteColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        Positioned(
                          bottom: 4,
                          right: 10,
                          child: Container(
                            height: 40,
                            width: 40,
                            padding: EdgeInsets.zero,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: whiteColor,
                            ),
                            child:
                                playerData.isLoad
                                    ? CustomCircularLoading()
                                    : IconButton(
                                      onPressed: () {
                                        final result = ref
                                            .read(
                                              playerModelHiveProvider.notifier,
                                            )
                                            .add(
                                              PlayerModelHive(
                                                playerTag:
                                                    playerData.playerInfo.tag,
                                                playerName:
                                                    playerData.playerInfo.name,
                                                clanTag:
                                                    playerData
                                                        .playerInfo
                                                        .playerClan
                                                        .tag,
                                                clanName:
                                                    playerData
                                                        .playerInfo
                                                        .playerClan
                                                        .name,
                                              ),
                                            );
                                        if (result == 'added') {
                                          SnackShow.showSuccess(
                                            context,
                                            'Added to favourites',
                                            isDarkMode,
                                          );
                                        }
                                      },
                                      icon: Icon(
                                        favTag.contains(
                                              playerData.playerInfo.tag,
                                            )
                                            ? Icons.favorite
                                            : Icons.favorite_border_outlined,
                                        color: const Color(0xFF6D7F97),
                                      ),
                                    ),
                          ),
                        ),
                      ],
                    ),
                    Container(
                      color: Theme.of(context).colorScheme.surface,
                      // decoration: BoxDecoration(
                      //   image: DecorationImage(
                      //     image: AssetImage('assets/images/cr_bg.jpg'),
                      //     fit: BoxFit.fitWidth,
                      //   ),
                      // ),
                      height: 40.h,
                      child: TabBar(
                        dividerHeight: 0.1,
                        isScrollable: true,
                        unselectedLabelColor: Colors.grey[600],
                        indicatorSize: TabBarIndicatorSize.label,
                        labelStyle: TextStyles.bodyTextStyles.copyWith(
                          fontSize: 9.sp,
                        ),
                        tabAlignment: TabAlignment.start,
                        padding: EdgeInsets.zero,
                        indicatorColor: Colors.transparent,

                        tabs: const [
                          Tab(text: 'Profile'),
                          Tab(text: 'Upcoming Chest'),
                          Tab(text: 'Battle Log'),
                        ],
                      ),
                    ),
                    Expanded(
                      child: TabBarView(
                        children: [
                          ProfileTab(playerModel: playerData.playerInfo),
                          UpcomingChestTab(playerTag: widget.playerTag),
                          BattleTab(playerTag: widget.playerTag),
                        ],
                      ),
                    ),
                  ],
                ),
      ),
    );
  }
}
