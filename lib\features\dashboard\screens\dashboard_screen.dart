import 'package:animations/animations.dart';
import 'package:cr/constant/color_constants.dart';
import 'package:cr/constant/common_snackbar.dart';
import 'package:cr/features/clan/screens/clan_screen.dart';
import 'package:cr/features/player/screens/player_screen.dart';
import 'package:cr/features/setting/screens/setting_screen.dart';
import 'package:cr/theme/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

final bottomNavIndexProvider = StateProvider<int>((ref) => 0);

// ignore: must_be_immutable
class DashboardScreen extends ConsumerWidget {
  DashboardScreen({super.key});
  DateTime? lastPressed;

  static final List<Widget> _widgetOptions = <Widget>[
    ClanScreen(),
    PlayerScreen(),
    SettingScreen(),
  ];

  @override
  Widget build(BuildContext context, ref) {
    int selectedIndex = ref.watch(bottomNavIndexProvider);
    final isDarkMode = ref.watch(themeProvider).isDarkMode;
    final themeState = ref.watch(themeProvider);
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, dynamic result) {
        final now = DateTime.now();

        if (lastPressed == null ||
            now.difference(lastPressed!) > const Duration(seconds: 2)) {
          lastPressed = now;
          // Show a SnackBar to prompt the user
          ScaffoldMessenger.of(context).clearSnackBars();
          SnackShow.showSuccess(context, 'Press again to exit', isDarkMode);
        } else {
          SystemNavigator.pop();
        }
      },
      child: Scaffold(
        body: PageTransitionSwitcher(
          transitionBuilder: (
            Widget child,
            Animation<double> primaryAnimation,
            Animation<double> secondaryAnimation,
          ) {
            return SharedAxisTransition(
              animation: primaryAnimation,
              secondaryAnimation: secondaryAnimation,
              transitionType: SharedAxisTransitionType.horizontal,
              child: child,
            );
          },
          child: _widgetOptions[selectedIndex],
        ),
        bottomNavigationBar: Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
          ),
          child: NavigationBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            indicatorColor: Colors.transparent,
            indicatorShape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            labelTextStyle: WidgetStatePropertyAll(
              TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12.sp,
                color: themeState.isDarkMode ? darkTextColor : textColor,
              ),
            ),
            labelBehavior: NavigationDestinationLabelBehavior.alwaysHide,
            height: 60.h,
            selectedIndex: selectedIndex,
            onDestinationSelected: (index) {
              ref.read(bottomNavIndexProvider.notifier).state = index;
            },
            destinations: [
              NavigationDestination(
                icon: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 20.w,
                    vertical: 5.h,
                  ),
                  decoration: BoxDecoration(
                    color:
                        selectedIndex == 0
                            ? themeState.isDarkMode
                                ? darkbgColor
                                : bgColor
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow:
                        selectedIndex == 0
                            ? [
                              BoxShadow(
                                color:
                                    themeState.isDarkMode
                                        ? Colors.black.withValues(alpha: 0.4)
                                        : Colors.white.withValues(alpha: 0.9),
                                offset: const Offset(-2, -2),
                                blurRadius: 4,
                                spreadRadius: 1,
                              ),
                              BoxShadow(
                                color:
                                    themeState.isDarkMode
                                        ? Colors.black.withValues(alpha: 0.4)
                                        : const Color(
                                          0xFFA3B1C6,
                                        ).withValues(alpha: 0.9),
                                offset: const Offset(2, 2),
                                blurRadius: 4,
                                spreadRadius: 1,
                              ),
                            ]
                            : null,
                  ),
                  child: Image.asset(
                    'assets/nav/clan.png',
                    width: 24.w,
                    height: 24.h,
                    color: themeState.isDarkMode ? darkTextColor : textColor,
                  ),
                ),
                label: 'Clan',
              ),
              NavigationDestination(
                icon: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 20.w,
                    vertical: 5.h,
                  ),
                  decoration: BoxDecoration(
                    color:
                        selectedIndex == 1
                            ? themeState.isDarkMode
                                ? darkbgColor
                                : bgColor
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow:
                        selectedIndex == 1
                            ? [
                              BoxShadow(
                                color:
                                    themeState.isDarkMode
                                        ? Colors.black.withValues(alpha: 0.4)
                                        : Colors.white.withValues(alpha: 0.9),
                                offset: const Offset(-2, -2),
                                blurRadius: 4,
                                spreadRadius: 1,
                              ),
                              BoxShadow(
                                color:
                                    themeState.isDarkMode
                                        ? Colors.black.withValues(alpha: 0.4)
                                        : const Color(
                                          0xFFA3B1C6,
                                        ).withValues(alpha: 0.9),
                                offset: const Offset(2, 2),
                                blurRadius: 4,
                                spreadRadius: 1,
                              ),
                            ]
                            : null,
                  ),
                  child: Image.asset(
                    'assets/nav/joystick.png',
                    width: 24.w,
                    height: 24.h,
                    color: themeState.isDarkMode ? darkTextColor : textColor,
                  ),
                ),
                label: 'Player',
              ),
              NavigationDestination(
                icon: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 20.w,
                    vertical: 5.h,
                  ),
                  decoration: BoxDecoration(
                    color:
                        selectedIndex == 2
                            ? themeState.isDarkMode
                                ? darkbgColor
                                : bgColor
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow:
                        selectedIndex == 2
                            ? [
                              BoxShadow(
                                color:
                                    themeState.isDarkMode
                                        ? Colors.black.withValues(alpha: 0.4)
                                        : Colors.white.withValues(alpha: 0.9),
                                offset: const Offset(-2, -2),
                                blurRadius: 4,
                                spreadRadius: 1,
                              ),
                              BoxShadow(
                                color:
                                    themeState.isDarkMode
                                        ? Colors.black.withValues(alpha: 0.4)
                                        : const Color(
                                          0xFFA3B1C6,
                                        ).withValues(alpha: 0.9),
                                offset: const Offset(2, 2),
                                blurRadius: 4,
                                spreadRadius: 1,
                              ),
                            ]
                            : null,
                  ),
                  child: Image.asset(
                    'assets/nav/settings.png',
                    width: 24.w,
                    height: 24.h,
                    color: themeState.isDarkMode ? darkTextColor : textColor,
                  ),
                ),
                label: 'Setting',
              ),
            ],
          ),
        ),
      ),
    );
  }
}
