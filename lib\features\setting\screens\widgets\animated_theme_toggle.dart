import 'package:cr/constant/color_constants.dart';
import 'package:cr/theme/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AnimatedThemeToggle extends ConsumerStatefulWidget {
  const AnimatedThemeToggle({super.key});

  @override
  ConsumerState<AnimatedThemeToggle> createState() => _AnimatedThemeToggleState();
}

class _AnimatedThemeToggleState extends ConsumerState<AnimatedThemeToggle>
    with TickerProviderStateMixin {
  late AnimationController _toggleController;
  late AnimationController _sunController;
  late AnimationController _moonController;
  late Animation<double> _toggleAnimation;
  late Animation<double> _sunRotation;
  late Animation<double> _moonRotation;
  late Animation<Color?> _backgroundColorAnimation;

  @override
  void initState() {
    super.initState();
    
    _toggleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _sunController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _moonController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _toggleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _toggleController,
      curve: Curves.easeInOut,
    ));

    _sunRotation = Tween<double>(
      begin: 0.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _sunController,
      curve: Curves.linear,
    ));

    _moonRotation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _moonController,
      curve: Curves.easeInOut,
    ));

    _backgroundColorAnimation = ColorTween(
      begin: const Color(0xFF87CEEB), // Light blue for day
      end: const Color(0xFF2C3E50), // Dark blue for night
    ).animate(CurvedAnimation(
      parent: _toggleController,
      curve: Curves.easeInOut,
    ));

    // Start continuous animations
    _sunController.repeat();
    _moonController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _toggleController.dispose();
    _sunController.dispose();
    _moonController.dispose();
    super.dispose();
  }

  void _handleToggle() {
    final themeState = ref.read(themeProvider);
    
    if (themeState.isDarkMode) {
      _toggleController.reverse();
      themeState.setLightTheme();
    } else {
      _toggleController.forward();
      themeState.setDarkTheme();
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeState = ref.watch(themeProvider);
    
    // Sync animation with current theme
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (themeState.isDarkMode && _toggleController.value == 0.0) {
        _toggleController.forward();
      } else if (!themeState.isDarkMode && _toggleController.value == 1.0) {
        _toggleController.reverse();
      }
    });

    return GestureDetector(
      onTap: _handleToggle,
      child: AnimatedBuilder(
        animation: Listenable.merge([
          _toggleController,
          _sunController,
          _moonController,
        ]),
        builder: (context, child) {
          return Container(
            width: 200.w,
            height: 100.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(50.r),
              gradient: LinearGradient(
                colors: [
                  _backgroundColorAnimation.value ?? const Color(0xFF87CEEB),
                  (_backgroundColorAnimation.value ?? const Color(0xFF87CEEB))
                      .withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: themeState.isDarkMode
                      ? Colors.black.withValues(alpha: 0.3)
                      : Colors.white.withValues(alpha: 0.8),
                  offset: const Offset(-4, -4),
                  blurRadius: 8,
                  spreadRadius: 1,
                ),
                BoxShadow(
                  color: themeState.isDarkMode
                      ? Colors.black.withValues(alpha: 0.3)
                      : const Color(0xFFA3B1C6).withValues(alpha: 0.6),
                  offset: const Offset(4, 4),
                  blurRadius: 8,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Stack(
              children: [
                // Stars for night mode
                if (_toggleAnimation.value > 0.3)
                  ...List.generate(5, (index) {
                    return Positioned(
                      left: 20.w + (index * 30.w),
                      top: 15.h + (index % 2 * 20.h),
                      child: Opacity(
                        opacity: _toggleAnimation.value,
                        child: Icon(
                          Icons.star,
                          color: Colors.white.withValues(alpha: 0.8),
                          size: 8.sp + (index % 2 * 4.sp),
                        ),
                      ),
                    );
                  }),
                
                // Animated toggle button
                AnimatedPositioned(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                  left: _toggleAnimation.value * (200.w - 80.w) + 10.w,
                  top: 10.h,
                  child: Container(
                    width: 80.w,
                    height: 80.h,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: themeState.isDarkMode ? darkbgColor : bgColor,
                      boxShadow: [
                        BoxShadow(
                          color: sPrimaryColor.withValues(alpha: 0.3),
                          blurRadius: 10,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: Center(
                      child: _toggleAnimation.value < 0.5
                          ? Transform.rotate(
                              angle: _sunRotation.value * 3.14159,
                              child: Icon(
                                Icons.wb_sunny,
                                color: const Color(0xFFFFD700),
                                size: 32.sp,
                              ),
                            )
                          : Transform.rotate(
                              angle: _moonRotation.value * 0.5,
                              child: Icon(
                                Icons.nightlight_round,
                                color: const Color(0xFFC0C0C0),
                                size: 32.sp,
                              ),
                            ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
