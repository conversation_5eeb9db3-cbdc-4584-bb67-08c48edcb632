{"buildFiles": ["C:\\Users\\<USER>\\.shorebird\\bin\\cache\\flutter\\fb8d194ceb0794310387baff34d657edad7219b8\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\royal stat\\cr\\android\\app\\.cxx\\RelWithDebInfo\\2b5c3h45\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\royal stat\\cr\\android\\app\\.cxx\\RelWithDebInfo\\2b5c3h45\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}