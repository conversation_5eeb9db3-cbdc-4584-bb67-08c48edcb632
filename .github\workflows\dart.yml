on:
  pull_request:
    branches:
      - main
      - master
  push:
    branches:
      - main
name: "Build Apks"
jobs:
  build:
    name: Build Apks
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v4
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.29.2'
          channel: 'stable'
      - run: flutter pub get
        # working-directory: directory_name
      - run: flutter build apk --release
        # working-directory: ctevt_admin
      
      # Upload artifact (for workflow storage)
      - name: Upload APK Artifact
        uses: actions/upload-artifact@v4
        with:
          name: apks
          path: build/app/outputs/flutter-apk/app-release.apk
      
      # Create GitHub Release (only on push to main branch)
      - name: Create Release
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        uses: softprops/action-gh-release@v1
        with:
          tag_name: v1.0.${{ github.run_number }}
          name: Release v1.0.${{ github.run_number }}
          body: |
            Automated release from commit ${{ github.sha }}
            
            ## Changes
            - Built from branch: ${{ github.ref_name }}
            - Commit: ${{ github.event.head_commit.message }}
          files: build/app/outputs/flutter-apk/app-release.apk
          draft: false
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
