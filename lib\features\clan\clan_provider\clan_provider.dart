import 'dart:developer';

import 'package:cr/features/clan/model/clan_info_model.dart';
import 'package:cr/features/clan/model/clan_model.dart';
import 'package:cr/features/clan/model/river_race_model.dart';
import 'package:cr/network/api.dart';
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final clanProvider = FutureProvider.family(
  (ref, String locationId) async =>
      ClanProvider.getClansByLocation(locationId: locationId),
);

final clanInfoProvider = FutureProvider.family(
  (ref, String clanTag) async => ClanProvider.getClanInfo(clanTag: clanTag),
);

final clanRiverRaceLogProvider = FutureProvider.family(
  (ref, String clanTag) async =>
      ClanProvider.getClanRiverRaceLog(clanTag: clanTag),
);

class ClanProvider {
  //get clans ranking by location
  static Future<List<ClanModel>> getClansByLocation({
    required String locationId,
  }) async {
    try {
      final res = await Dio().get(
        '${Api.getLocation}/$locationId/rankings/clans',
        options: Options(headers: {'Authorization': Api.token}),
      );
      final data =
          (res.data['items'] as List)
              .map((e) => ClanModel.fromJson(e))
              .toList();

      return data;
    } on DioException catch (e) {
      if (e.response != null) {
        log(
          'Error in getting data ${e.response!.statusCode} : ${e.response!.statusMessage}',
        );
        throw ('${e.response!.data['message']}');
      } else {
        log('Error in getting data: ${e.message}');
      }
      throw ('${e.message}');
    }
  }

  // get clan info
  static Future<ClanInfoModel> getClanInfo({required String clanTag}) async {
    try {
      final res = await Dio().get(
        '${Api.getClan}/%23$clanTag',
        options: Options(headers: {'Authorization': Api.token}),
      );
      final data = ClanInfoModel.fromJson(res.data);

      return data;
    } on DioException catch (e) {
      if (e.response != null) {
        log(
          'Error in getting data ${e.response!.statusCode} : ${e.response!.statusMessage}',
        );
        throw ('${e.response!.data['reason']}');
      } else {
        log('Error in getting data: ${e.message}');
      }
      throw ('${e.message}');
    }
  }

  // get clan river race log
  static Future<List<RiverRaceModel>> getClanRiverRaceLog({
    required String clanTag,
  }) async {
    try {
      final res = await Dio().get(
        '${Api.getClan}/%23$clanTag/riverracelog',
        options: Options(headers: {'Authorization': Api.token}),
      );
      final data =
          (res.data['items'] as List)
              .map((e) => RiverRaceModel.fromJson(e))
              .toList();
      return data;
    } on DioException catch (e) {
      if (e.response != null) {
        log(
          'Error in getting data ${e.response!.statusCode} : ${e.response!.statusMessage}',
        );
        throw ('${e.response!.data['reason']}');
      } else {
        log('Error in getting data: ${e.message}');
      }
      throw ('${e.message}');
    }
  }
}
