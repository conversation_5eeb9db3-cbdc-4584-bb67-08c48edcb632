image: cimg/android:2023.08

stages:
  - build
  - release

variables:
  ANDROID_BUILD_DIR: "build/app/outputs/apk/release"
  FLUTTER_VERSION: "3.29.2"
  FLUTTER_HOME: "${CI_PROJECT_DIR}/flutter"
  PUB_CACHE: "${CI_PROJECT_DIR}/.pub-cache"

cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - flutter/
    - .pub-cache/
    - .dart_tool/
    - build/
    - .gradle/

before_script:
  - sudo apt-get update
  - sudo apt-get install -y ninja-build cmake
  - |
    if [ ! -d "${FLUTTER_HOME}" ]; then
      echo "Installing Flutter SDK..."
      mkdir -p ${FLUTTER_HOME}
      wget -q -O flutter.tar.xz "https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_${FLUTTER_VERSION}-stable.tar.xz"
      tar -xf flutter.tar.xz -C ${CI_PROJECT_DIR}
      rm flutter.tar.xz
    else
      echo "Using cached Flutter SDK"
    fi
  - export PATH="$PATH:${FLUTTER_HOME}/bin"
  - export PUB_CACHE="${PUB_CACHE}"
  - flutter --version
  - flutter precache --android
  - flutter pub get

build_android:
  stage: build
  script:
    - |
      if [[ -n "$KEYSTORE_BASE64" ]]; then
        echo "Setting up keystore for app signing..."
        echo $KEYSTORE_BASE64 | base64 -d > android/app/upload-keystore.jks
        echo "storePassword=$KEY_STORE_PASSWORD" > android/key.properties
        echo "keyPassword=$KEY_PASSWORD" >> android/key.properties
        echo "keyAlias=$KEY_ALIAS" >> android/key.properties
        echo "storeFile=upload-keystore.jks" >> android/key.properties
      elif [[ -f "android/app/upload-keystore.jks" ]]; then
        echo "Using existing keystore file in repository"
      else
        echo "No keystore provided - building unsigned APK"
      fi
    - flutter build apk --release
    - |
      if [[ -f "$ANDROID_BUILD_DIR/app-release.apk" ]]; then
        echo "✅ APK build successful"
      else
        echo "❌ APK build failed"
        exit 1
      fi
  artifacts:
    paths:
      - $ANDROID_BUILD_DIR/app-release.apk
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "master"

release_apk:
  stage: release
  needs: ["build_android"]
  script:
    - apk_url="${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/jobs/artifacts/${CI_COMMIT_BRANCH}/raw/${ANDROID_BUILD_DIR}/app-release.apk?job=build_android"
    - release_tag="v${CI_PIPELINE_ID}"
    - |
      curl --request POST --header "PRIVATE-TOKEN: $GITLAB_TOKEN" \
        --data "name=Release ${release_tag}" \
        --data "tag_name=${release_tag}" \
        --data "description=Download the APK [here](${apk_url})" \
        "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/releases"
  rules:
    - if: $CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "master"
