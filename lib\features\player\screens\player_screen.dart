import 'package:cr/constant/color_constants.dart';
import 'package:cr/constant/common_snackbar.dart';
import 'package:cr/constant/custom_container.dart';
import 'package:cr/constant/custom_text_field.dart';
import 'package:cr/constant/navigation.dart';
import 'package:cr/features/hive/screens/fav_player_screen.dart';
import 'package:cr/features/player/screens/player_info_screen.dart';
import 'package:cr/theme/theme_provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PlayerScreen extends ConsumerWidget {
  PlayerScreen({super.key});
  final TextEditingController playerTagController = TextEditingController();

  @override
  Widget build(BuildContext context, ref) {
    final themeState = ref.watch(themeProvider);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Player'),
        elevation: 0,
        actions: [
          Container(
            margin: EdgeInsets.only(right: 16.w),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: themeState.isDarkMode ? darkbgColor : bgColor,
                shape: BoxShape.circle,
                boxShadow: ContainerBoxShadows.getBoxShadow(ref),
              ),
              child: IconButton(
                icon: Icon(
                  Icons.favorite,
                  color: themeState.isDarkMode ? darkTextColor : textColor,
                  size: 22,
                ),
                onPressed: () {
                  navigatePush(context, FavPlayerScreen());
                },
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        child: Column(
          children: [
            SizedBox(height: 16.h),
            Container(
              height: 45.h,
              decoration: BoxDecoration(
                color: themeState.isDarkMode ? darkbgColor : bgColor,
                borderRadius: BorderRadius.circular(24.r),
                boxShadow: ContainerBoxShadows.getBoxShadow(ref),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.only(left: 20.w),
                      decoration: BoxDecoration(
                        color: themeState.isDarkMode ? darkbgColor : bgColor,
                        borderRadius: BorderRadius.circular(24.r),
                      ),
                      child: CustomTextField(
                        controller: playerTagController,
                        isDarkMode: themeState.isDarkMode,
                        hintText: 'Search Player (exclude #)',
                      ),
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.all(4.r),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: themeState.isDarkMode ? darkbgColor : bgColor,
                      boxShadow: ContainerBoxShadows.getBoxShadow(ref),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(24.r),
                        onTap: () {
                          FocusScope.of(context).unfocus();
                          if (playerTagController.text.trim().isEmpty) {
                            SnackShow.showFailure(
                              context,
                              'Enter Player Tag',
                              themeState.isDarkMode,
                            );
                          } else {
                            navigatePush(
                              context,
                              PlayerInfoScreen(
                                playerTag: playerTagController.text.trim(),
                              ),
                            );
                          }
                        },
                        child: Container(
                          width: 40.w,
                          height: 40.h,
                          alignment: Alignment.center,
                          child: Icon(
                            CupertinoIcons.search,
                            color:
                                themeState.isDarkMode ? bgColor : darkbgColor,
                            size: 20.sp,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
