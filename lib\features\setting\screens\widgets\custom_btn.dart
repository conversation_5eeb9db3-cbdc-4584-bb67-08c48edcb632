import 'package:cr/constant/color_constants.dart';
import 'package:cr/theme/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomBtn extends ConsumerStatefulWidget {
  final String iconPath;
  final void Function()? onTap;
  final Color borderColor;
  final bool isSelected;

  const CustomBtn({
    super.key,
    required this.iconPath,
    required this.onTap,
    required this.borderColor,
    this.isSelected = false,
  });

  @override
  ConsumerState<CustomBtn> createState() => _CustomBtnState();
}

class _CustomBtnState extends ConsumerState<CustomBtn>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _glowAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    _animationController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _handleTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final themeState = ref.watch(themeProvider);

    return GestureDetector(
      onTap: widget.onTap,
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: 65.w,
              height: 65.h,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: themeState.isDarkMode ? darkbgColor : bgColor,
                borderRadius: BorderRadius.circular(16.r),
                border: Border.all(
                  color: widget.isSelected ? sPrimaryColor : widget.borderColor,
                  width: widget.isSelected ? 3 : 2,
                ),
                boxShadow: [
                  // Neumorphism shadow effect
                  BoxShadow(
                    color:
                        themeState.isDarkMode
                            ? Colors.black.withValues(alpha: 0.3)
                            : Colors.white.withValues(alpha: 0.8),
                    offset: const Offset(-4, -4),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                  BoxShadow(
                    color:
                        themeState.isDarkMode
                            ? Colors.black.withValues(alpha: 0.3)
                            : const Color(0xFFA3B1C6).withValues(alpha: 0.6),
                    offset: const Offset(4, 4),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                  // Glow effect when selected
                  if (widget.isSelected)
                    BoxShadow(
                      color: sPrimaryColor.withValues(
                        alpha: 0.4 * _glowAnimation.value,
                      ),
                      blurRadius: 12,
                      spreadRadius: 2,
                    ),
                ],
              ),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                child: Image.asset(
                  widget.iconPath,
                  width: 28.w,
                  height: 28.h,
                  color:
                      widget.isSelected
                          ? sPrimaryColor
                          : (themeState.isDarkMode ? darkTextColor : textColor),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
