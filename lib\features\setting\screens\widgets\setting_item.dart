import 'package:cr/constant/color_constants.dart';
import 'package:cr/constant/text_styles.dart';
import 'package:cr/theme/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SettingItem extends ConsumerWidget {
  final IconData icon;
  final String title;
  final void Function()? onTap;
  const SettingItem({
    super.key,
    required this.title,
    required this.onTap,
    required this.icon,
  });

  @override
  Widget build(BuildContext context, ref) {
    final themeState = ref.watch(themeProvider);
    return InkWell(
      highlightColor: Colors.transparent,
      splashColor: Colors.transparent,
      splashFactory: NoSplash.splashFactory,
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 12.h,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: themeState.isDarkMode ? darkTextColor : textColor,
                size: 18.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                title,
                style: TextStyles.titleTextStyles.copyWith(
                  fontSize: 12.sp,
                  color: themeState.isDarkMode ? darkTextColor : textColor,
                ),
              ),
              Spacer(),
              Icon(
                Icons.arrow_forward_ios,
                size: 16.sp,
                color: themeState.isDarkMode ? darkTextColor : textColor,
              ),
            ],
          ),
          Divider(thickness: 0.2),
        ],
      ),
    );
  }
}
