import 'package:cr/constant/color_constants.dart';
import 'package:cr/constant/text_styles.dart';
import 'package:cr/theme/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SettingItem extends ConsumerStatefulWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final void Function()? onTap;
  final Widget? trailing;
  final Color? iconColor;

  const SettingItem({
    super.key,
    required this.title,
    required this.onTap,
    required this.icon,
    this.subtitle,
    this.trailing,
    this.iconColor,
  });

  @override
  ConsumerState<SettingItem> createState() => _SettingItemState();
}

class _SettingItemState extends ConsumerState<SettingItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.98).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    _animationController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _handleTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final themeState = ref.watch(themeProvider);

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6.h),
      child: GestureDetector(
        onTap: widget.onTap,
        onTapDown: _handleTapDown,
        onTapUp: _handleTapUp,
        onTapCancel: _handleTapCancel,
        child: MouseRegion(
          onEnter: (_) => setState(() => _isHovered = true),
          onExit: (_) => setState(() => _isHovered = false),
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: EdgeInsets.symmetric(
                    horizontal: 20.w,
                    vertical: 16.h,
                  ),
                  decoration: BoxDecoration(
                    color: themeState.isDarkMode ? darkbgColor : bgColor,
                    borderRadius: BorderRadius.circular(16.r),
                    boxShadow: [
                      // Neumorphism effect
                      BoxShadow(
                        color:
                            themeState.isDarkMode
                                ? Colors.black.withValues(alpha: 0.3)
                                : Colors.white.withValues(alpha: 0.8),
                        offset: const Offset(-4, -4),
                        blurRadius: 8,
                        spreadRadius: 1,
                      ),
                      BoxShadow(
                        color:
                            themeState.isDarkMode
                                ? Colors.black.withValues(alpha: 0.3)
                                : const Color(
                                  0xFFA3B1C6,
                                ).withValues(alpha: 0.6),
                        offset: const Offset(4, 4),
                        blurRadius: 8,
                        spreadRadius: 1,
                      ),
                      // Hover glow effect
                      if (_isHovered)
                        BoxShadow(
                          color: sPrimaryColor.withValues(alpha: 0.2),
                          blurRadius: 12,
                          spreadRadius: 2,
                        ),
                    ],
                  ),
                  child: Row(
                    children: [
                      // Icon with background
                      Container(
                        width: 40.w,
                        height: 40.h,
                        decoration: BoxDecoration(
                          color:
                              widget.iconColor?.withValues(alpha: 0.1) ??
                              sPrimaryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Icon(
                          widget.icon,
                          color: widget.iconColor ?? sPrimaryColor,
                          size: 20.sp,
                        ),
                      ),
                      SizedBox(width: 16.w),

                      // Title and subtitle
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.title,
                              style: TextStyles.titleTextStyles.copyWith(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w600,
                                color:
                                    themeState.isDarkMode
                                        ? darkTextColor
                                        : textColor,
                              ),
                            ),
                            if (widget.subtitle != null) ...[
                              SizedBox(height: 2.h),
                              Text(
                                widget.subtitle!,
                                style: TextStyles.bodyTextStyles.copyWith(
                                  fontSize: 11.sp,
                                  color: (themeState.isDarkMode
                                          ? darkTextColor
                                          : textColor)
                                      .withValues(alpha: 0.7),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),

                      // Trailing widget or arrow
                      widget.trailing ??
                          AnimatedRotation(
                            turns: _isHovered ? 0.1 : 0.0,
                            duration: const Duration(milliseconds: 200),
                            child: Icon(
                              Icons.arrow_forward_ios,
                              size: 16.sp,
                              color:
                                  themeState.isDarkMode
                                      ? darkTextColor.withValues(alpha: 0.6)
                                      : textColor.withValues(alpha: 0.6),
                            ),
                          ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
