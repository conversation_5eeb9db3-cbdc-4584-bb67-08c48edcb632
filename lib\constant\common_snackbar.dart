import 'package:cr/constant/color_constants.dart';
import 'package:cr/constant/text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SnackShow {
  static showSuccess(BuildContext context, String message, bool isDarkMode) {
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.transparent,
        elevation: 0,
        duration: Duration(seconds: 2),
        content: Container(
          padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
          margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
          decoration: BoxDecoration(
            color: isDarkMode ? darkbgColor : bgColor,
            borderRadius: BorderRadius.circular(8.r),
            boxShadow: [
              BoxShadow(
                color:
                    isDarkMode
                        ? Colors.black.withValues(alpha: 0.4)
                        : Colors.white.withValues(alpha: 0.9),
                offset: const Offset(-8, -8),
                blurRadius: 16,
                spreadRadius: 3,
              ),
              BoxShadow(
                color:
                    isDarkMode
                        ? Colors.black.withValues(alpha: 0.4)
                        : const Color(0xFFA3B1C6).withValues(alpha: 0.9),
                offset: const Offset(8, 8),
                blurRadius: 16,
                spreadRadius: 3,
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(6.r),
                decoration: BoxDecoration(
                  color: isDarkMode ? darkbgColor : bgColor,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color:
                          isDarkMode
                              ? Colors.black.withValues(alpha: 0.4)
                              : Colors.white.withValues(alpha: 0.9),
                      offset: const Offset(-3, -3),
                      blurRadius: 6,
                      spreadRadius: 1,
                    ),
                    BoxShadow(
                      color:
                          isDarkMode
                              ? Colors.black.withValues(alpha: 0.4)
                              : const Color(0xFFA3B1C6).withValues(alpha: 0.9),
                      offset: const Offset(3, 3),
                      blurRadius: 6,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Icon(
                  Icons.check_circle_outline,
                  color: const Color(0xFF4CAF50),
                  size: 16.sp,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  message,
                  style: TextStyles.bodyTextWhiteStyles.copyWith(
                    fontSize: 9.sp,
                    color: isDarkMode ? darkTextColor : textColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static showFailure(BuildContext context, String message, bool isDarkMode) {
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.transparent,
        elevation: 0,
        duration: Duration(seconds: 2),
        content: Container(
          padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
          margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
          decoration: BoxDecoration(
            color: isDarkMode ? darkbgColor : bgColor,
            borderRadius: BorderRadius.circular(8.r),
            boxShadow: [
              BoxShadow(
                color:
                    isDarkMode
                        ? Colors.black.withValues(alpha: 0.4)
                        : Colors.white.withValues(alpha: 0.9),
                offset: const Offset(-8, -8),
                blurRadius: 16,
                spreadRadius: 3,
              ),
              BoxShadow(
                color:
                    isDarkMode
                        ? Colors.black.withValues(alpha: 0.4)
                        : const Color(0xFFA3B1C6).withValues(alpha: 0.9),
                offset: const Offset(8, 8),
                blurRadius: 16,
                spreadRadius: 3,
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(6.r),
                decoration: BoxDecoration(
                  color: isDarkMode ? darkbgColor : bgColor,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color:
                          isDarkMode
                              ? Colors.black.withValues(alpha: 0.4)
                              : Colors.white.withValues(alpha: 0.9),
                      offset: const Offset(-3, -3),
                      blurRadius: 6,
                      spreadRadius: 1,
                    ),
                    BoxShadow(
                      color:
                          isDarkMode
                              ? Colors.black.withValues(alpha: 0.4)
                              : const Color(0xFFA3B1C6).withValues(alpha: 0.9),
                      offset: const Offset(3, 3),
                      blurRadius: 6,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Icon(
                  Icons.error_outline,
                  color: const Color(0xFFE53935),
                  size: 16.sp,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  message,
                  style: TextStyles.bodyTextWhiteStyles.copyWith(
                    fontSize: 9.sp,
                    color: isDarkMode ? darkTextColor : textColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
