import 'package:cr/constant/color_constants.dart';
import 'package:cr/constant/custom_circular_loading.dart';
import 'package:cr/constant/custom_container.dart';
import 'package:cr/constant/text_styles.dart';
import 'package:cr/features/upcoming_chest/provider/upcoming_chest_provider.dart';
import 'package:cr/theme/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class UpcomingChestTab extends ConsumerWidget {
  final String playerTag;
  const UpcomingChestTab({super.key, required this.playerTag});

  @override
  Widget build(BuildContext context, ref) {
    final chestList = ref.watch(upcomingChestProvider(playerTag));
    final isDarkMode = ref.watch(themeProvider).isDarkMode;
    return Container(
      decoration: BoxDecoration(color: Theme.of(context).colorScheme.surface),
      child: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(upcomingChestProvider);
          return Future.delayed(const Duration(seconds: 2));
        },
        child:
            chestList.isLoad
                ? Center(child: CustomCircularLoading())
                : chestList.isError
                ? Center(child: Text(chestList.errMessage))
                : MasonryGridView.builder(
                  key: const PageStorageKey<String>('upcoming_chest_tab'),
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 8.h,
                  ),
                  gridDelegate:
                      const SliverSimpleGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                      ),
                  mainAxisSpacing: 14.h,
                  crossAxisSpacing: 14.w,
                  itemCount: chestList.upcomingChestsList.length,
                  itemBuilder: (BuildContext context, int index) {
                    final chestData = chestList.upcomingChestsList[index];
                    return Container(
                      height: 80.h,
                      width: 60.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: Theme.of(context).colorScheme.surface,
                        boxShadow: ContainerBoxShadows.getBoxShadow(ref),
                      ),
                      child: Column(
                        children: [
                          Expanded(
                            child: Center(
                              child: Text(
                                chestData.name,
                                textAlign: TextAlign.center,
                                style: TextStyles.bodyTextWhiteStyles.copyWith(
                                  fontSize: 9.sp,
                                  color: isDarkMode ? darkTextColor : textColor,
                                ),
                              ),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(vertical: 2.h),
                            width: double.infinity,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(9.r),
                                bottomRight: Radius.circular(9.r),
                              ),
                              color: Theme.of(context).colorScheme.surface,
                              border: Border.all(color: Colors.white),
                            ),
                            child: Text(
                              chestData.index == 0
                                  ? 'next'
                                  : chestData.index.toString(),
                              textAlign: TextAlign.center,
                              style: TextStyles.bodyTextStyles.copyWith(
                                fontSize: 9.sp,
                                color: isDarkMode ? darkTextColor : textColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
      ),
    );
  }
}
