import 'package:cr/constant/color_constants.dart';
import 'package:cr/constant/common_snackbar.dart';
import 'package:cr/constant/custom_container.dart';
import 'package:cr/constant/text_styles.dart';
import 'package:cr/features/hive/provider/player_provider_hive.dart';
import 'package:cr/theme/theme_provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FavPlayerScreen extends ConsumerWidget {
  const FavPlayerScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final favPlayer = ref.watch(playerModelHiveProvider);
    final themeState = ref.watch(themeProvider);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Fav Player'),
        leading: InkWell(
          onTap: () => Navigator.pop(context),
          child: Icon(CupertinoIcons.back),
        ),
        iconTheme: IconThemeData(
          color: themeState.isDarkMode ? darkTextColor : textColor,
        ),
      ),
      body:
          favPlayer.isEmpty
              ? Center(
                child: Text(
                  'The List is Empty',
                  style: TextStyles.bodyTextWhiteStyles.copyWith(
                    fontSize: 10.sp,
                    color: const Color(0xFF6D7F97),
                  ),
                ),
              )
              : ListView.separated(
                padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 16.h),
                separatorBuilder: (context, index) => SizedBox(height: 16.h),
                itemCount: favPlayer.length,
                itemBuilder: (context, index) {
                  final player = favPlayer[index];
                  return CupertinoContextMenu(
                    actions: [
                      CupertinoContextMenuAction(
                        trailingIcon: CupertinoIcons.trash,
                        onPressed: () {
                          final res = ref
                              .read(playerModelHiveProvider.notifier)
                              .remove(player);
                          if (res == 'deleted') {
                            Navigator.pop(context);
                          }
                        },
                        child: Text(
                          'Delete',
                          style: TextStyles.bodyTextWhiteStyles.copyWith(
                            fontSize: 10.sp,
                            color: Colors.red,
                          ),
                        ),
                      ),
                      CupertinoContextMenuAction(
                        trailingIcon: Icons.copy_outlined,
                        onPressed: () {
                          Clipboard.setData(
                            ClipboardData(text: player.playerTag),
                          );
                          Navigator.pop(context);
                          //show snackbar
                          SnackShow.showSuccess(
                            context,
                            'Copied Player Tag',
                            themeState.isDarkMode,
                          );
                        },
                        child: Text(
                          'Copy Player Tag',
                          style: TextStyles.bodyTextWhiteStyles.copyWith(
                            fontSize: 10.sp,
                            color:
                                themeState.isDarkMode
                                    ? darkTextColor
                                    : textColor,
                          ),
                        ),
                      ),
                      CupertinoContextMenuAction(
                        trailingIcon: Icons.copy_outlined,
                        onPressed: () {
                          Clipboard.setData(
                            ClipboardData(text: player.clanTag),
                          );
                          Navigator.pop(context);
                          SnackShow.showSuccess(
                            context,
                            'Copied Player Clan Tag',
                            themeState.isDarkMode,
                          );
                        },
                        child: Text(
                          'Copy Player Clan Tag',
                          style: TextStyles.bodyTextWhiteStyles.copyWith(
                            fontSize: 10.sp,
                            color:
                                themeState.isDarkMode
                                    ? darkTextColor
                                    : textColor,
                          ),
                        ),
                      ),
                    ],
                    child: SizedBox(
                      width: MediaQuery.of(context).size.width,
                      height: MediaQuery.of(context).size.height * 0.07,
                      child: Row(
                        children: [
                          Container(
                            alignment: Alignment.center,
                            width: 30.w,
                            height: 30.h,
                            decoration: BoxDecoration(
                              color:
                                  themeState.isDarkMode ? darkbgColor : bgColor,
                              shape: BoxShape.circle,
                              boxShadow: ContainerBoxShadows.getBoxShadow(ref),
                            ),
                            child: Text(
                              '${index + 1}',
                              style: TextStyles.bodyTextWhiteStyles.copyWith(
                                fontSize: 10.sp,
                                color:
                                    themeState.isDarkMode
                                        ? darkTextColor
                                        : textColor,
                              ),
                            ),
                          ),
                          SizedBox(width: 14.w),
                          Expanded(
                            child: Container(
                              padding: EdgeInsets.fromLTRB(12.w, 8.h, 0, 8.h),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8.r),
                                color:
                                    themeState.isDarkMode
                                        ? darkbgColor
                                        : bgColor,
                                boxShadow: ContainerBoxShadows.getBoxShadow(
                                  ref,
                                ),
                              ),
                              child: Column(
                                spacing: 4.h,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    player.playerName,
                                    style: TextStyles.bodyTextWhiteStyles
                                        .copyWith(
                                          fontSize: 12.sp,
                                          color:
                                              themeState.isDarkMode
                                                  ? darkTextColor
                                                  : textColor,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                  ),
                                  Text(
                                    player.clanName,
                                    style: TextStyles.bodyTextWhiteStyles
                                        .copyWith(
                                          fontSize: 9.sp,
                                          color:
                                              themeState.isDarkMode
                                                  ? darkTextColor
                                                  : textColor,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
    );
  }
}
