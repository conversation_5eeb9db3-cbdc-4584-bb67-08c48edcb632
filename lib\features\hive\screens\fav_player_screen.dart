import 'package:cr/constant/color_constants.dart';
import 'package:cr/constant/common_snackbar.dart';
import 'package:cr/constant/text_styles.dart';
import 'package:cr/features/hive/provider/player_provider_hive.dart';
import 'package:cr/theme/theme_provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FavPlayerScreen extends ConsumerWidget {
  const FavPlayerScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final favPlayer = ref.watch(playerModelHiveProvider);
    final themeState = ref.watch(themeProvider);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Fav Player'),
        leading: InkWell(
          onTap: () => Navigator.pop(context),
          child: Icon(CupertinoIcons.back),
        ),
        iconTheme: IconThemeData(
          color: themeState.isDarkMode ? darkTextColor : textColor,
        ),
      ),
      body:
          favPlayer.isEmpty
              ? _buildEmptyState(themeState.isDarkMode)
              : _buildPlayerList(favPlayer, ref, themeState, context),
    );
  }

  Widget _buildEmptyState(bool isDarkMode) {
    return Center(
      child: Container(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(24.w),
              decoration: BoxDecoration(
                color: isDarkMode ? darkbgColor : bgColor,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color:
                        isDarkMode
                            ? Colors.black.withValues(alpha: 0.3)
                            : Colors.white.withValues(alpha: 0.8),
                    offset: const Offset(-4, -4),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                  BoxShadow(
                    color:
                        isDarkMode
                            ? Colors.black.withValues(alpha: 0.3)
                            : const Color(0xFFA3B1C6).withValues(alpha: 0.6),
                    offset: const Offset(4, 4),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Icon(
                Icons.person_outline,
                size: 48.sp,
                color: sPrimaryColor.withValues(alpha: 0.6),
              ),
            ),
            SizedBox(height: 24.h),
            Text(
              'No Favorite Players',
              style: TextStyles.titleTextStyles.copyWith(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: isDarkMode ? darkTextColor : textColor,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Add players to your favorites to see them here',
              style: TextStyles.bodyTextStyles.copyWith(
                fontSize: 12.sp,
                color: (isDarkMode ? darkTextColor : textColor).withValues(
                  alpha: 0.7,
                ),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlayerList(
    List favPlayer,
    WidgetRef ref,
    themeState,
    BuildContext context,
  ) {
    return ListView.separated(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
      separatorBuilder: (context, index) => SizedBox(height: 12.h),
      itemCount: favPlayer.length,
      itemBuilder: (context, index) {
        final player = favPlayer[index];
        return _buildPlayerCard(player, index, ref, themeState, context);
      },
    );
  }

  Widget _buildPlayerCard(
    player,
    int index,
    WidgetRef ref,
    themeState,
    BuildContext context,
  ) {
    return CupertinoContextMenu(
      actions: [
        CupertinoContextMenuAction(
          trailingIcon: CupertinoIcons.trash,
          onPressed: () {
            final res = ref
                .read(playerModelHiveProvider.notifier)
                .remove(player);
            if (res == 'deleted') {
              Navigator.pop(context);
            }
          },
          child: Text(
            'Delete',
            style: TextStyles.bodyTextWhiteStyles.copyWith(
              fontSize: 10.sp,
              color: Colors.red,
            ),
          ),
        ),
        CupertinoContextMenuAction(
          trailingIcon: Icons.copy_outlined,
          onPressed: () {
            Clipboard.setData(ClipboardData(text: player.playerTag));
            Navigator.pop(context);
            //show snackbar
            SnackShow.showSuccess(
              context,
              'Copied Player Tag',
              themeState.isDarkMode,
            );
          },
          child: Text(
            'Copy Player Tag',
            style: TextStyles.bodyTextWhiteStyles.copyWith(
              fontSize: 10.sp,
              color: themeState.isDarkMode ? darkTextColor : textColor,
            ),
          ),
        ),
        CupertinoContextMenuAction(
          trailingIcon: Icons.copy_outlined,
          onPressed: () {
            Clipboard.setData(ClipboardData(text: player.clanTag));
            Navigator.pop(context);
            SnackShow.showSuccess(
              context,
              'Copied Player Clan Tag',
              themeState.isDarkMode,
            );
          },
          child: Text(
            'Copy Player Clan Tag',
            style: TextStyles.bodyTextWhiteStyles.copyWith(
              fontSize: 10.sp,
              color: themeState.isDarkMode ? darkTextColor : textColor,
            ),
          ),
        ),
      ],
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: themeState.isDarkMode ? darkbgColor : bgColor,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color:
                  themeState.isDarkMode
                      ? Colors.black.withValues(alpha: 0.3)
                      : Colors.white.withValues(alpha: 0.8),
              offset: const Offset(-4, -4),
              blurRadius: 8,
              spreadRadius: 1,
            ),
            BoxShadow(
              color:
                  themeState.isDarkMode
                      ? Colors.black.withValues(alpha: 0.3)
                      : const Color(0xFFA3B1C6).withValues(alpha: 0.6),
              offset: const Offset(4, 4),
              blurRadius: 8,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Row(
          children: [
            // Rank Badge
            Container(
              width: 40.w,
              height: 40.h,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [sPrimaryColor.withValues(alpha: 0.8), sPrimaryColor],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: sPrimaryColor.withValues(alpha: 0.3),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  '${index + 1}',
                  style: TextStyles.titleTextStyles.copyWith(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            SizedBox(width: 16.w),

            // Player Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.person, size: 16.sp, color: sPrimaryColor),
                      SizedBox(width: 6.w),
                      Expanded(
                        child: Text(
                          player.playerName,
                          style: TextStyles.titleTextStyles.copyWith(
                            fontSize: 13.sp,
                            fontWeight: FontWeight.w600,
                            color:
                                themeState.isDarkMode
                                    ? darkTextColor
                                    : textColor,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 6.h),
                  Row(
                    children: [
                      Icon(
                        Icons.groups,
                        size: 14.sp,
                        color: sPrimaryColor.withValues(alpha: 0.7),
                      ),
                      SizedBox(width: 6.w),
                      Expanded(
                        child: Text(
                          player.clanName,
                          style: TextStyles.bodyTextStyles.copyWith(
                            fontSize: 11.sp,
                            color: (themeState.isDarkMode
                                    ? darkTextColor
                                    : textColor)
                                .withValues(alpha: 0.8),
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Action Indicator
            Container(
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: sPrimaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(Icons.more_vert, size: 16.sp, color: sPrimaryColor),
            ),
          ],
        ),
      ),
    );
  }
}
