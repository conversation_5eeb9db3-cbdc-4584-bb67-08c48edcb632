import 'package:cached_network_image/cached_network_image.dart';
import 'package:cr/constant/color_constants.dart';
import 'package:cr/constant/custom_container.dart';
import 'package:cr/constant/text_styles.dart';
import 'package:cr/features/player/model/player_model.dart';
import 'package:cr/features/player/provider/player_provider.dart';
import 'package:cr/theme/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class ProfileTab extends ConsumerWidget {
  final PlayerModel playerModel;
  const ProfileTab({super.key, required this.playerModel});

  // get color
  Color getColor(String color) {
    switch (color.toLowerCase()) {
      case 'common':
        return Colors.grey;
      case 'rare':
        return Colors.orange;
      case 'epic':
        return Colors.purpleAccent;
      case 'legendary':
        return Colors.blueAccent;
      case 'champiion':
        return Colors.yellowAccent;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context, ref) {
    final isDarkMode = ref.watch(themeProvider).isDarkMode;
    return Container(
      decoration: BoxDecoration(color: Theme.of(context).colorScheme.surface),
      child: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(playerInfoProvider);
          return Future.delayed(const Duration(seconds: 2));
        },
        child: SingleChildScrollView(
          key: const PageStorageKey<String>('profileTab'),
          child: SafeArea(
            top: false,
            child: Column(
              spacing: 4.h,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.all(12.w),
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 18.h,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: ContainerBoxShadows.getBoxShadow(ref),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    spacing: 4.h,
                    children: [
                      Text(
                        'Trophy Road',
                        style: TextStyles.bodyTextWhiteStyles.copyWith(
                          fontSize: 12.sp,
                          color: isDarkMode ? darkTextColor : textColor,
                        ),
                      ),
                      SizedBox(height: 2.h),
                      _buildInfoRow(
                        'Highest Trophies',
                        playerModel.bestTrophies.toString(),
                        isDarkMode,
                      ),
                      _buildInfoRow(
                        'Current Trophies',
                        playerModel.trophies.toString(),
                        isDarkMode,
                      ),
                      _buildInfoRow(
                        'Your Arena',
                        playerModel.arena.name,
                        isDarkMode,
                      ),

                      _buildInfoRow(
                        'Legacy Best',
                        playerModel.legacyTrophyRoadHighScore.toString(),
                        isDarkMode,
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 12.w),
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 18.h,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: ContainerBoxShadows.getBoxShadow(ref),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    spacing: 4.h,
                    children: [
                      Text(
                        'Path of Legends',
                        style: TextStyles.bodyTextWhiteStyles.copyWith(
                          fontSize: 12.sp,
                          color: isDarkMode ? darkTextColor : textColor,
                        ),
                      ),
                      SizedBox(height: 2.h),
                      _buildInfoRow(
                        'League',
                        playerModel.pathOfLegends.leagueNumber.toString(),
                        isDarkMode,
                      ),
                      _buildInfoRow(
                        'Trophies',
                        playerModel.pathOfLegends.trophies.toString(),
                        isDarkMode,
                      ),
                      _buildInfoRow(
                        'Rank',
                        playerModel.pathOfLegends.rank.toString(),
                        isDarkMode,
                      ),
                      SizedBox(height: 8.h),
                      Container(
                        alignment: Alignment.center,
                        padding: EdgeInsets.symmetric(
                          horizontal: 6.w,
                          vertical: 4.h,
                        ),
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: ContainerBoxShadows.getBoxShadow(ref),
                        ),
                        child: Text(
                          'Previous Season',
                          style: TextStyles.bodyTextWhiteStyles.copyWith(
                            fontSize: 12.sp,
                            color: isDarkMode ? darkTextColor : textColor,
                          ),
                        ),
                      ),
                      SizedBox(height: 8.h),
                      _buildInfoRow(
                        'League',
                        playerModel.prevPathOfLegends.leagueNumber.toString(),
                        isDarkMode,
                      ),
                      _buildInfoRow(
                        'Trophies',
                        playerModel.prevPathOfLegends.trophies.toString(),
                        isDarkMode,
                      ),
                      _buildInfoRow(
                        'Rank',
                        playerModel.prevPathOfLegends.rank.toString(),
                        isDarkMode,
                      ),
                      SizedBox(height: 8.h),
                      Container(
                        alignment: Alignment.center,
                        padding: EdgeInsets.symmetric(
                          horizontal: 6.w,
                          vertical: 4.h,
                        ),
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: ContainerBoxShadows.getBoxShadow(ref),
                        ),
                        child: Text(
                          'Best Season',
                          style: TextStyles.bodyTextWhiteStyles.copyWith(
                            fontSize: 12.sp,
                            color: isDarkMode ? darkTextColor : textColor,
                          ),
                        ),
                      ),
                      SizedBox(height: 8.h),
                      _buildInfoRow(
                        'League',
                        playerModel.bestPathOfLegends.leagueNumber.toString(),
                        isDarkMode,
                      ),
                      _buildInfoRow(
                        'Trophies',
                        playerModel.bestPathOfLegends.trophies.toString(),
                        isDarkMode,
                      ),
                      _buildInfoRow(
                        'Rank',
                        playerModel.bestPathOfLegends.rank.toString(),
                        isDarkMode,
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.all(12.w),
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 18.h,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: ContainerBoxShadows.getBoxShadow(ref),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    spacing: 4.h,
                    children: [
                      Text(
                        'Stats Royale',
                        style: TextStyles.bodyTextWhiteStyles.copyWith(
                          fontSize: 12.sp,
                          color: isDarkMode ? darkTextColor : textColor,
                        ),
                      ),
                      SizedBox(height: 2.h),
                      _buildInfoRow(
                        'Wins',
                        playerModel.wins.toString(),
                        isDarkMode,
                      ),
                      _buildInfoRow(
                        'Losses',
                        playerModel.losses.toString(),
                        isDarkMode,
                      ),
                      _buildInfoRow(
                        'Matched Played',
                        playerModel.battleCount.toString(),
                        isDarkMode,
                      ),
                      _buildInfoRow(
                        '3 Crowns Wins',
                        playerModel.threeCrownWins.toString(),
                        isDarkMode,
                      ),
                      _buildInfoRow(
                        'Donations',
                        playerModel.totalDonations.toString(),
                        isDarkMode,
                      ),
                      _buildInfoRow(
                        'Current Donations',
                        playerModel.donations.toString(),
                        isDarkMode,
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 12.w),
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 18.h,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: ContainerBoxShadows.getBoxShadow(ref),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    spacing: 4.h,
                    children: [
                      Text(
                        'Active Deck',
                        style: TextStyles.bodyTextWhiteStyles.copyWith(
                          fontSize: 12.sp,
                          color: isDarkMode ? darkTextColor : textColor,
                        ),
                      ),
                      MasonryGridView.builder(
                        key: const PageStorageKey<String>('profileTab'),
                        padding: EdgeInsets.zero,
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        gridDelegate:
                            const SliverSimpleGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 4,
                            ),
                        mainAxisSpacing: 2.h,
                        crossAxisSpacing: 4.w,
                        itemCount: playerModel.currentDeck.length,
                        itemBuilder: (BuildContext context, int index) {
                          final currentDeck = playerModel.currentDeck[index];
                          return Stack(
                            alignment: Alignment.center,
                            children: [
                              Container(
                                padding: EdgeInsets.only(bottom: 8.h),
                                child: Image.network(
                                  currentDeck.iconUrl.medium,
                                  height: 125.h,
                                  width: 125.w,
                                  fit: BoxFit.fill,
                                ),
                              ),
                              Positioned(
                                bottom: 0,
                                child: Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 10.w,
                                    vertical: 1.h,
                                  ),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4),
                                    color: getColor(
                                      currentDeck.rarity,
                                    ).withValues(alpha: 0.8),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withValues(
                                          alpha: 0.2,
                                        ),
                                        offset: const Offset(0, 2),
                                        blurRadius: 4,
                                      ),
                                    ],
                                  ),
                                  child: Text(
                                    'Level ${currentDeck.level}',
                                    style: TextStyles.bodyTextWhiteStyles
                                        .copyWith(
                                          fontSize: 9.sp,
                                          color: Colors.white,
                                        ),
                                  ),
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.all(12.w),
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 18.h,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: ContainerBoxShadows.getBoxShadow(ref),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    spacing: 4.h,
                    children: [
                      Text(
                        'Badges',
                        style: TextStyles.bodyTextWhiteStyles.copyWith(
                          fontSize: 12.sp,
                          color: isDarkMode ? darkTextColor : textColor,
                        ),
                      ),
                      MasonryGridView.builder(
                        key: const PageStorageKey<String>('profileTab'),
                        padding: EdgeInsets.zero,
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        gridDelegate:
                            const SliverSimpleGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 6,
                            ),
                        mainAxisSpacing: 0.h,
                        crossAxisSpacing: 0.w,
                        itemCount: playerModel.badges.length,
                        itemBuilder: (BuildContext context, int index) {
                          final currentDeck = playerModel.badges[index];
                          return CachedNetworkImage(
                            imageUrl: currentDeck.iconUrl.large,
                            height: 50.h,
                            width: 30.w,
                            placeholder:
                                (context, url) => Transform.scale(
                                  scale: 0.3.r,
                                  child: const CircularProgressIndicator(
                                    color: Color(0xFF6D7F97),
                                  ),
                                ),
                            errorWidget:
                                (context, url, error) => Icon(
                                  Icons.error,
                                  color: const Color(0xFF6D7F97),
                                ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 12.w),
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 18.h,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: ContainerBoxShadows.getBoxShadow(ref),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    spacing: 4.h,
                    children: [
                      Text(
                        'Achievements',
                        style: TextStyles.bodyTextWhiteStyles.copyWith(
                          fontSize: 12.sp,
                          color: isDarkMode ? darkTextColor : textColor,
                        ),
                      ),
                      SizedBox(height: 2.h),
                      ListView.separated(
                        key: const PageStorageKey<String>('profileTab'),
                        separatorBuilder:
                            (context, index) => SizedBox(height: 4.h),
                        padding: EdgeInsets.zero,
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemCount: playerModel.achievements.length,
                        itemBuilder: (BuildContext context, int index) {
                          final achievement = playerModel.achievements[index];
                          return Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                achievement.name,
                                style: TextStyles.bodyTextWhiteStyles.copyWith(
                                  fontSize: 9.sp,
                                  color: isDarkMode ? darkTextColor : textColor,
                                ),
                              ),
                              Text(
                                '${achievement.value}/${achievement.target}',
                                style: TextStyles.bodyTextWhiteStyles.copyWith(
                                  fontSize: 9.sp,
                                  color: isDarkMode ? darkTextColor : textColor,
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 10.h),
                Center(
                  child: Text(
                    'Clash Royale Stats',
                    style: TextStyles.bodyTextWhiteStyles.copyWith(
                      fontSize: 10.sp,
                      color: isDarkMode ? darkTextColor : textColor,
                    ),
                  ),
                ),
                SizedBox(height: 10.h),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, bool isDarkMode) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyles.bodyTextWhiteStyles.copyWith(
            fontSize: 9.sp,
            color: isDarkMode ? darkTextColor : textColor,
          ),
        ),
        Text(
          value,
          style: TextStyles.bodyTextWhiteStyles.copyWith(
            fontSize: 9.sp,
            color: isDarkMode ? darkTextColor : textColor,
          ),
        ),
      ],
    );
  }
}
