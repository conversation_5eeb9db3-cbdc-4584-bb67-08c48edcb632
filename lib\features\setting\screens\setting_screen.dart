import 'dart:developer';

import 'package:cr/constant/color_constants.dart';
import 'package:cr/constant/navigation.dart';
import 'package:cr/constant/text_styles.dart';
import 'package:cr/features/about/screens/about_screen.dart';
import 'package:cr/features/setting/screens/widgets/custom_btn.dart';
import 'package:cr/features/setting/screens/widgets/setting_item.dart';
import 'package:cr/theme/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:package_info_plus/package_info_plus.dart';

class SettingScreen extends ConsumerStatefulWidget {
  const SettingScreen({super.key});

  @override
  ConsumerState<SettingScreen> createState() => _SettingScreenState();
}

class _SettingScreenState extends ConsumerState<SettingScreen> {
  PackageInfo _packageInfo = PackageInfo(
    appName: 'Unknown',
    packageName: 'Unknown',
    version: 'Unknown',
    buildNumber: 'Unknown',
    buildSignature: 'Unknown',
    installerStore: 'Unknown',
  );

  @override
  void initState() {
    super.initState();
    _initPackageInfo();
  }

  Future<void> _initPackageInfo() async {
    final info = await PackageInfo.fromPlatform();
    setState(() {
      _packageInfo = info;
    });
  }

  @override
  Widget build(BuildContext context) {
    final themeState = ref.watch(themeProvider);
    return Scaffold(
      appBar: AppBar(title: const Text('Setting')),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        child: Column(
          spacing: 12.h,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 12.h),
            Center(
              child: Container(
                decoration: BoxDecoration(
                  color: themeState.isDarkMode ? bgColor : darkbgColor,
                  borderRadius: BorderRadius.circular(24.r),
                  border: Border.all(
                    color: themeState.isDarkMode ? darkTextColor : textColor,
                    width: 2,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(24.r),
                  child: Image.asset(
                    'assets/icon/icon.png',
                    height: 150,
                    width: 150,
                  ),
                ),
              ),
            ),
            SizedBox(height: 4.h),
            Row(
              spacing: 6.w,
              children: [
                Icon(
                  Icons.palette_outlined,
                  color: themeState.isDarkMode ? darkTextColor : textColor,
                  size: 18.sp,
                ),
                Text(
                  'Appearance',
                  style: TextStyles.titleTextStyles.copyWith(
                    fontSize: 12.sp,
                    color: themeState.isDarkMode ? darkTextColor : textColor,
                  ),
                ),
              ],
            ),
            Row(
              spacing: 16.w,
              children: [
                CustomBtn(
                  onTap: () {
                    themeState.setLightTheme();
                  },
                  iconPath: 'assets/images/sun.png',
                  borderColor:
                      themeState.isDarkMode
                          ? const Color(0xFFA3B1C6)
                          : sPrimaryColor,
                ),
                CustomBtn(
                  iconPath: 'assets/images/moon.png',
                  onTap: () {
                    themeState.setDarkTheme();
                  },
                  borderColor:
                      themeState.isDarkMode
                          ? sPrimaryColor
                          : const Color(0xFFA3B1C6),
                ),
              ],
            ),
            Divider(thickness: 0.2),
            SettingItem(
              title: 'Privacy Policy',
              icon: Icons.privacy_tip_outlined,
              onTap: () {
                log('Privacy Policy');
              },
            ),
            SettingItem(
              title: 'About',
              icon: Icons.info_outline,
              onTap: () {
                navigatePush(context, AboutScreen());
              },
            ),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.symmetric(vertical: 4.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Version ',
              style: TextStyles.bodyTextStyles.copyWith(
                fontSize: 8.sp,
                color: Colors.grey,
              ),
            ),
            Text(
              _packageInfo.version,
              textAlign: TextAlign.center,
              style: TextStyles.bodyTextStyles.copyWith(
                fontSize: 8.sp,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
