name: cr
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev
version: 0.0.01+1

environment:
  sdk: ^3.7.0

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8
  dio: ^5.8.0+1
  flutter_riverpod: ^2.6.1
  dartz: ^0.10.1
  flutter_screenutil: ^5.9.3
  animations: ^2.0.11
  flutter_staggered_grid_view: ^0.7.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  dropdown_search: ^6.0.2
  flutter_native_splash: ^2.4.2
  intl: ^0.20.2
  cached_network_image: ^3.4.1
  google_mobile_ads: ^6.0.0
  package_info_plus: ^8.3.0





flutter_native_splash:
  color: "#343434"
  branding: assets/icon/branding.png
  branding_mode: bottom
  # image: assets/images/splash.png
  android_12:
    # image: assets/images/splash.png
    # color_dark: "#404141"
    branding: assets/icon/branding.png
    branding_mode: bottom
    icon_background_color: "#ffffff"
    icon_background_color_dark: "#ffffff" 

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0
  flutter_launcher_icons: "^0.14.3"
  build_runner: ^2.4.15
  hive_generator: ^2.0.1

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icon/icon.png"
  min_sdk_android: 21
  remove_alpha_ios: true


flutter:

  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/nav/
    - assets/images/
    - assets/icon/
    - shorebird.yaml

  # example:
  fonts:
    - family: Supercell-Magic-Regular
      fonts:
        - asset: fonts/Supercell-Magic Regular.ttf